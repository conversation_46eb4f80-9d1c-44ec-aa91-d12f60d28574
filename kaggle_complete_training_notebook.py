"""
Complete Kaggle Training Notebook for YOLO 80%+ Accuracy
Copy this entire script into a Kaggle notebook and run it
"""

# ============================================================================
# CELL 1: Install Dependencies and Setup
# ============================================================================

import os
import sys

# Install required packages
!pip install ultralytics albumentations scikit-learn

# Import libraries
import torch
import numpy as np
import random
import yaml
from ultralytics import YOLO
from collections import Counter
import glob
import shutil
from pathlib import Path
import cv2
import albumentations as A
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

print("🚀 Kaggle YOLO Training Setup Complete!")
print(f"🔧 PyTorch version: {torch.__version__}")
print(f"🔧 CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# ============================================================================
# CELL 2: Dataset Analysis and Preparation
# ============================================================================

def analyze_dataset():
    """Analyze the dataset structure and class distribution"""
    
    # Dataset paths
    train_labels_dir = "/kaggle/input/poker-clubs-suits/First.v2i.yolov8/train/labels"
    val_labels_dir = "/kaggle/input/poker-clubs-suits/First.v2i.yolov8/valid/labels"
    
    # Class names
    class_names = ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 
                   'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 
                   'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']
    
    def count_classes(labels_dir):
        class_counts = Counter()
        label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                for line in f:
                    if line.strip():
                        class_id = int(line.strip().split()[0])
                        class_counts[class_id] += 1
        return class_counts
    
    train_counts = count_classes(train_labels_dir)
    val_counts = count_classes(val_labels_dir)
    
    print("📊 Dataset Analysis Results:")
    print("=" * 50)
    print(f"Training images: {len(glob.glob('/kaggle/input/poker-clubs-suits/First.v2i.yolov8/train/images/*'))}")
    print(f"Validation images: {len(glob.glob('/kaggle/input/poker-clubs-suits/First.v2i.yolov8/valid/images/*'))}")
    
    print("\n📈 Training set class distribution:")
    for class_id, count in sorted(train_counts.items()):
        class_name = class_names[class_id] if class_id < len(class_names) else f'Unknown_{class_id}'
        print(f'Class {class_id:2d} ({class_name:12s}): {count:4d} instances')
    
    # Identify underrepresented classes
    if train_counts:
        max_count = max(train_counts.values())
        min_count = min(train_counts.values())
        print(f'\n⚖️ Class imbalance ratio: {max_count/min_count:.2f}:1')
        
        threshold = 80  # Target minimum samples
        underrepresented = [class_id for class_id, count in train_counts.items() if count < threshold]
        if underrepresented:
            print(f'🔴 Underrepresented classes (< {threshold} samples): {len(underrepresented)} classes')
            for class_id in underrepresented:
                class_name = class_names[class_id] if class_id < len(class_names) else f'Unknown_{class_id}'
                print(f'  - Class {class_id} ({class_name}): {train_counts[class_id]} samples')
    
    return train_counts, val_counts

# Run dataset analysis
train_counts, val_counts = analyze_dataset()

# ============================================================================
# CELL 3: Data Augmentation for Class Balancing
# ============================================================================

def augment_underrepresented_classes():
    """Augment underrepresented classes to achieve balance"""
    
    # Paths
    train_images_dir = "/kaggle/input/poker-clubs-suits/First.v2i.yolov8/train/images"
    train_labels_dir = "/kaggle/input/poker-clubs-suits/First.v2i.yolov8/train/labels"
    
    # Create working directories
    work_images_dir = "/kaggle/working/train_images"
    work_labels_dir = "/kaggle/working/train_labels"
    os.makedirs(work_images_dir, exist_ok=True)
    os.makedirs(work_labels_dir, exist_ok=True)
    
    # Copy original data to working directory
    print("📁 Copying original dataset to working directory...")
    for img_file in glob.glob(os.path.join(train_images_dir, "*")):
        shutil.copy2(img_file, work_images_dir)
    for label_file in glob.glob(os.path.join(train_labels_dir, "*")):
        shutil.copy2(label_file, work_labels_dir)
    
    # Class names
    class_names = ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 
                   'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 
                   'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']
    
    # Augmentation pipeline
    heavy_augment = A.Compose([
        A.HorizontalFlip(p=0.5),
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
        A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.8),
        A.RandomGamma(gamma_limit=(80, 120), p=0.5),
        A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
        A.MotionBlur(blur_limit=3, p=0.3),
        A.RandomRotate90(p=0.3),
        A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=15, p=0.5),
    ])
    
    # Analyze current distribution
    class_counts = Counter()
    for label_file in glob.glob(os.path.join(work_labels_dir, '*.txt')):
        with open(label_file, 'r') as f:
            for line in f:
                if line.strip():
                    class_id = int(line.strip().split()[0])
                    class_counts[class_id] += 1
    
    target_min_samples = 80
    underrepresented = {class_id: count for class_id, count in class_counts.items() 
                       if count < target_min_samples}
    
    print(f"🔄 Augmenting {len(underrepresented)} underrepresented classes...")
    
    for class_id, current_count in underrepresented.items():
        needed_samples = target_min_samples - current_count
        class_name = class_names[class_id] if class_id < len(class_names) else f'Unknown_{class_id}'
        print(f"Class {class_id:2d} ({class_name:12s}): {current_count:3d} -> {target_min_samples:3d} (+{needed_samples:2d})")
        
        # Find images containing this class
        class_images = []
        for label_file in glob.glob(os.path.join(work_labels_dir, '*.txt')):
            with open(label_file, 'r') as f:
                if any(int(line.strip().split()[0]) == class_id for line in f if line.strip()):
                    image_name = os.path.basename(label_file).replace('.txt', '')
                    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                        img_path = os.path.join(work_images_dir, image_name + ext)
                        if os.path.exists(img_path):
                            class_images.append((img_path, label_file))
                            break
        
        # Generate augmented samples
        for i in range(needed_samples):
            if not class_images:
                continue
                
            img_path, label_path = random.choice(class_images)
            
            # Load and augment image
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            augmented = heavy_augment(image=image)
            augmented_image = cv2.cvtColor(augmented['image'], cv2.COLOR_RGB2BGR)
            
            # Save augmented image and copy label
            base_name = os.path.splitext(os.path.basename(img_path))[0]
            aug_img_name = f"{base_name}_aug_{class_id}_{i}.jpg"
            aug_label_name = f"{base_name}_aug_{class_id}_{i}.txt"
            
            cv2.imwrite(os.path.join(work_images_dir, aug_img_name), augmented_image)
            shutil.copy2(label_path, os.path.join(work_labels_dir, aug_label_name))
    
    print("✅ Data augmentation completed!")
    return work_images_dir, work_labels_dir

# Run data augmentation
work_images_dir, work_labels_dir = augment_underrepresented_classes()

# ============================================================================
# CELL 4: Create Kaggle Data Configuration
# ============================================================================

# Create data.yaml for training
kaggle_data_yaml = """
train: /kaggle/working/train_images
val: /kaggle/input/poker-clubs-suits/First.v2i.yolov8/valid/images

nc: 28
names: ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']
"""

with open('/kaggle/working/data.yaml', 'w') as f:
    f.write(kaggle_data_yaml)

print("📝 Created Kaggle data configuration file")

# ============================================================================
# CELL 5: Training with Optimized Parameters
# ============================================================================

def train_optimized_model():
    """Train YOLO model with optimized parameters for 80%+ accuracy"""
    
    # Set random seeds
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    print("🚀 Starting Optimized YOLO Training")
    print("=" * 50)
    
    # Check GPU
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 Using device: {device}")
    
    # Determine optimal batch size
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        if gpu_memory >= 15:
            batch_size = 32
        elif gpu_memory >= 8:
            batch_size = 24
        else:
            batch_size = 16
    else:
        batch_size = 8
    
    print(f"📦 Using batch size: {batch_size}")
    
    # Initialize model
    model = YOLO('yolo11n.pt')
    
    # Train with optimized parameters
    results = model.train(
        data='/kaggle/working/data.yaml',
        epochs=150,  # Reduced for Kaggle time limits
        patience=25,
        batch=batch_size,
        imgsz=640,
        save=True,
        save_period=15,
        cache=True,
        device=device,
        workers=2,
        project='/kaggle/working/runs/train',
        name='optimized_model',
        exist_ok=True,
        pretrained=True,
        optimizer='AdamW',
        verbose=True,
        seed=42,
        deterministic=True,
        cos_lr=True,
        close_mosaic=10,
        amp=True,
        # Optimized hyperparameters
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        box=7.5,
        cls=0.5,
        dfl=1.5,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        translate=0.1,
        scale=0.5,
        fliplr=0.5,
        mosaic=1.0,
    )
    
    return model, results

# Start training
print("🎯 Starting training process...")
model, results = train_optimized_model()

# ============================================================================
# CELL 6: Model Validation and Analysis
# ============================================================================

def analyze_model_performance(model):
    """Analyze per-class performance and identify weak classes"""
    
    print("🔍 Analyzing model performance...")
    
    # Validate model
    metrics = model.val()
    
    # Class names
    class_names = ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 
                   'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 
                   'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']
    
    print("\n📈 Detailed Per-Class Performance:")
    print("-" * 70)
    print(f"{'Class':<15} {'AP@0.5':<10} {'AP@0.5:0.95':<12} {'Status':<15}")
    print("-" * 70)
    
    weak_classes = []
    good_classes = []
    
    if hasattr(metrics, 'ap_class_index') and hasattr(metrics, 'ap'):
        for i, class_idx in enumerate(metrics.ap_class_index):
            if class_idx < len(class_names):
                class_name = class_names[class_idx]
                ap50 = metrics.ap50[i] if hasattr(metrics, 'ap50') else 0
                ap = metrics.ap[i] if hasattr(metrics, 'ap') else 0
                
                if ap50 > 0.8:
                    status = "✅ Excellent"
                    good_classes.append((class_name, ap50))
                elif ap50 > 0.6:
                    status = "⚠️ Good"
                    good_classes.append((class_name, ap50))
                else:
                    status = "❌ Needs Work"
                    weak_classes.append((class_name, ap50))
                
                print(f"{class_name:<15} {ap50:<10.3f} {ap:<12.3f} {status:<15}")
    
    # Summary
    print("\n📊 Performance Summary:")
    print(f"✅ Classes with 80%+ accuracy: {len([c for c in good_classes if c[1] > 0.8])}")
    print(f"⚠️ Classes with 60-80% accuracy: {len([c for c in good_classes if 0.6 <= c[1] <= 0.8])}")
    print(f"❌ Classes below 60% accuracy: {len(weak_classes)}")
    
    if weak_classes:
        print(f"\n⚠️ Classes needing improvement:")
        for class_name, ap50 in weak_classes:
            print(f"  - {class_name}: {ap50:.1%}")
    
    # Overall metrics
    if hasattr(metrics, 'box') and hasattr(metrics.box, 'map'):
        print(f"\n🎯 Overall mAP@0.5: {metrics.box.map50:.3f}")
        print(f"🎯 Overall mAP@0.5:0.95: {metrics.box.map:.3f}")
    
    return weak_classes, good_classes

# Analyze performance
weak_classes, good_classes = analyze_model_performance(model)

print("\n🎉 Training completed successfully!")
print("📁 Model saved at: /kaggle/working/runs/train/optimized_model/weights/best.pt")
print("📊 Training plots saved at: /kaggle/working/runs/train/optimized_model/")
print("💾 Download the best.pt file for deployment!")

# ============================================================================
# CELL 7: Display Training Results
# ============================================================================

# Display training curves
import matplotlib.pyplot as plt
import pandas as pd

try:
    results_csv = "/kaggle/working/runs/train/optimized_model/results.csv"
    if os.path.exists(results_csv):
        df = pd.read_csv(results_csv)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot training losses
        axes[0,0].plot(df['epoch'], df['train/box_loss'], label='Box Loss')
        axes[0,0].plot(df['epoch'], df['train/cls_loss'], label='Class Loss')
        axes[0,0].plot(df['epoch'], df['train/dfl_loss'], label='DFL Loss')
        axes[0,0].set_title('Training Losses')
        axes[0,0].set_xlabel('Epoch')
        axes[0,0].set_ylabel('Loss')
        axes[0,0].legend()
        axes[0,0].grid(True)
        
        # Plot validation metrics
        axes[0,1].plot(df['epoch'], df['val/box_loss'], label='Val Box Loss')
        axes[0,1].plot(df['epoch'], df['val/cls_loss'], label='Val Class Loss')
        axes[0,1].plot(df['epoch'], df['val/dfl_loss'], label='Val DFL Loss')
        axes[0,1].set_title('Validation Losses')
        axes[0,1].set_xlabel('Epoch')
        axes[0,1].set_ylabel('Loss')
        axes[0,1].legend()
        axes[0,1].grid(True)
        
        # Plot mAP
        axes[1,0].plot(df['epoch'], df['metrics/mAP50(B)'], label='mAP@0.5')
        axes[1,0].plot(df['epoch'], df['metrics/mAP50-95(B)'], label='mAP@0.5:0.95')
        axes[1,0].set_title('Mean Average Precision')
        axes[1,0].set_xlabel('Epoch')
        axes[1,0].set_ylabel('mAP')
        axes[1,0].legend()
        axes[1,0].grid(True)
        
        # Plot precision and recall
        axes[1,1].plot(df['epoch'], df['metrics/precision(B)'], label='Precision')
        axes[1,1].plot(df['epoch'], df['metrics/recall(B)'], label='Recall')
        axes[1,1].set_title('Precision and Recall')
        axes[1,1].set_xlabel('Epoch')
        axes[1,1].set_ylabel('Score')
        axes[1,1].legend()
        axes[1,1].grid(True)
        
        plt.tight_layout()
        plt.savefig('/kaggle/working/training_curves.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📈 Training curves displayed and saved!")
        
except Exception as e:
    print(f"⚠️ Could not display training curves: {e}")

print("\n🎯 TRAINING COMPLETE! 🎯")
print("=" * 50)
print("📥 Download these files:")
print("  - /kaggle/working/runs/train/optimized_model/weights/best.pt (trained model)")
print("  - /kaggle/working/training_curves.png (training visualization)")
print("  - /kaggle/working/runs/train/optimized_model/ (all training outputs)")
