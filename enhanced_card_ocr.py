"""
Enhanced Card OCR Tool - Optimized for Teen Patti Card Detection
Includes advanced preprocessing and region suggestions
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import os
import threading
import time
from collections import defaultdict, Counter

# OCR Engine imports with graceful fallback
OCR_ENGINES = {}

try:
    import ddddocr
    OCR_ENGINES['ddddocr'] = True
    print("✅ ddddocr available")
except ImportError:
    print("⚠️ ddddocr not available")

try:
    from paddleocr import PaddleOCR
    OCR_ENGINES['paddleocr'] = True
    print("✅ PaddleOCR available")
except ImportError:
    print("⚠️ PaddleOCR not available")

try:
    import pytesseract
    OCR_ENGINES['tesseract'] = True
    print("✅ Tesseract available")
except ImportError:
    print("⚠️ Tesseract not available")

class EnhancedCardOCR:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced Card OCR Tool - Teen Patti Optimized")
        self.root.geometry("1400x900")
        
        # Variables
        self.image_path = None
        self.original_image = None
        self.display_image = None
        self.canvas = None
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        self.selected_regions = []
        
        # OCR Engines
        self.ocr_engines = {}
        self.init_ocr_engines()
        
        # Setup GUI
        self.setup_gui()
        
    def init_ocr_engines(self):
        """Initialize available OCR engines"""
        print("🔧 Initializing OCR engines...")
        
        if OCR_ENGINES.get('ddddocr'):
            try:
                self.ocr_engines['ddddocr'] = ddddocr.DdddOcr(show_ad=False)
                print("✅ ddddocr initialized")
            except Exception as e:
                print(f"❌ ddddocr failed: {e}")
        
        if OCR_ENGINES.get('paddleocr'):
            try:
                self.ocr_engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                print("✅ PaddleOCR initialized")
            except Exception as e:
                print(f"❌ PaddleOCR failed: {e}")
        
        if OCR_ENGINES.get('tesseract'):
            try:
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = 'tesseract'
                print("✅ Tesseract initialized")
            except Exception as e:
                print(f"❌ Tesseract failed: {e}")
        
        print(f"📊 Total OCR engines available: {len(self.ocr_engines)}")
    
    def setup_gui(self):
        """Setup the GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="📁 Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🎯 Suggest Card Regions", command=self.suggest_card_regions).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🔄 Clear Selections", command=self.clear_selections).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🔍 Run OCR Analysis", command=self.run_ocr_comparison).pack(side=tk.LEFT, padx=(0, 10))
        
        # Options frame
        options_frame = ttk.Frame(control_frame)
        options_frame.pack(side=tk.RIGHT)
        
        # Preprocessing options
        self.preprocessing_var = tk.StringVar(value="enhanced")
        ttk.Label(options_frame, text="Preprocessing:").pack(side=tk.LEFT, padx=(10, 5))
        preprocessing_combo = ttk.Combobox(options_frame, textvariable=self.preprocessing_var, 
                                         values=["original", "grayscale", "enhanced", "card_optimized"], 
                                         state="readonly", width=12)
        preprocessing_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # Engine status
        engine_status = f"Engines: {', '.join(self.ocr_engines.keys()) if self.ocr_engines else 'None'}"
        ttk.Label(options_frame, text=engine_status, foreground="blue").pack(side=tk.LEFT)
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Image
        left_frame = ttk.LabelFrame(content_frame, text="Image & Region Selection")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(left_frame, bg='white', cursor='crosshair')
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Bind mouse events for region selection
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)
        
        # Right panel - Results
        right_frame = ttk.LabelFrame(content_frame, text="OCR Results & Analysis")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Results text area with scrollbar
        results_frame = ttk.Frame(right_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select an image to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
        # Instructions
        instructions = f"""
🎯 Enhanced Card OCR Tool - Teen Patti Optimized

Available Engines: {', '.join(self.ocr_engines.keys()) if self.ocr_engines else 'None'}

🃏 Card Detection Tips:
• Select SMALL regions around individual cards (not the entire game area)
• Use 'Suggest Card Regions' for automatic detection
• Try 'card_optimized' preprocessing for best results
• Look for card values: A, K, Q, J, 10, 9, 8, 7, 6, 5, 4, 3, 2
• Look for suits: ♠, ♥, ♦, ♣ (spades, hearts, diamonds, clubs)

📋 Instructions:
1. Click 'Select Image' or image auto-loads
2. Click 'Suggest Card Regions' for automatic detection
3. Or manually select small regions around individual cards
4. Choose preprocessing method (card_optimized recommended)
5. Click 'Run OCR Analysis' to compare engines
        """
        self.results_text.insert(tk.END, instructions)
    
    def select_image(self):
        """Select and load an image"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path):
        """Load and display the selected image"""
        try:
            self.image_path = file_path
            self.original_image = cv2.imread(file_path)
            
            if self.original_image is None:
                messagebox.showerror("Error", "Could not load the image file")
                return
            
            # Convert BGR to RGB for display
            rgb_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            
            # Resize image to fit canvas while maintaining aspect ratio
            canvas_width = 700
            canvas_height = 600
            
            h, w = rgb_image.shape[:2]
            scale = min(canvas_width/w, canvas_height/h)
            
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            resized_image = cv2.resize(rgb_image, (new_w, new_h))
            
            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(resized_image)
            self.display_image = ImageTk.PhotoImage(pil_image)
            
            # Update canvas
            self.canvas.delete("all")
            self.canvas.config(width=new_w, height=new_h)
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
            
            # Store scale factor for coordinate conversion
            self.scale_factor = scale
            
            self.status_var.set(f"Image loaded: {os.path.basename(file_path)} ({w}x{h})")
            self.clear_selections()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
    
    def suggest_card_regions(self):
        """Suggest regions that might contain cards"""
        if self.original_image is None:
            messagebox.showwarning("Warning", "Please load an image first")
            return
        
        self.status_var.set("Analyzing image for card regions...")
        
        # For Teen Patti, suggest typical card positions
        h, w = self.original_image.shape[:2]
        
        # Typical Teen Patti card positions (adjust based on your screenshot)
        suggested_regions = []
        
        # Left hand cards (3 cards)
        left_start_x = int(w * 0.15)  # 15% from left
        left_y = int(h * 0.6)         # 60% from top
        card_width = int(w * 0.08)    # 8% of image width
        card_height = int(h * 0.15)   # 15% of image height
        
        for i in range(3):
            x1 = left_start_x + i * int(card_width * 0.7)  # Overlap cards slightly
            y1 = left_y
            x2 = x1 + card_width
            y2 = y1 + card_height
            suggested_regions.append((x1, y1, x2, y2, f"Left Card {i+1}"))
        
        # Center hand cards (3 cards)
        center_start_x = int(w * 0.42)  # 42% from left
        for i in range(3):
            x1 = center_start_x + i * int(card_width * 0.7)
            y1 = left_y
            x2 = x1 + card_width
            y2 = y1 + card_height
            suggested_regions.append((x1, y1, x2, y2, f"Center Card {i+1}"))
        
        # Right hand cards (3 cards)
        right_start_x = int(w * 0.69)  # 69% from left
        for i in range(3):
            x1 = right_start_x + i * int(card_width * 0.7)
            y1 = left_y
            x2 = x1 + card_width
            y2 = y1 + card_height
            suggested_regions.append((x1, y1, x2, y2, f"Right Card {i+1}"))
        
        # Add suggested regions
        self.selected_regions = []
        for x1, y1, x2, y2, name in suggested_regions:
            # Ensure coordinates are within bounds
            x1 = max(0, min(x1, w))
            y1 = max(0, min(y1, h))
            x2 = max(0, min(x2, w))
            y2 = max(0, min(y2, h))
            
            if x2 > x1 and y2 > y1:
                self.selected_regions.append((x1, y1, x2, y2))
        
        # Redraw canvas with suggested regions
        self.redraw_canvas_with_regions()
        
        self.status_var.set(f"Suggested {len(self.selected_regions)} card regions")
    
    def redraw_canvas_with_regions(self):
        """Redraw canvas with all selected regions"""
        if self.canvas and self.display_image:
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
            
            # Draw all selected regions
            for i, (x1, y1, x2, y2) in enumerate(self.selected_regions):
                # Convert to display coordinates
                dx1 = int(x1 * self.scale_factor)
                dy1 = int(y1 * self.scale_factor)
                dx2 = int(x2 * self.scale_factor)
                dy2 = int(y2 * self.scale_factor)
                
                # Draw rectangle
                color = 'red' if i < 3 else 'blue' if i < 6 else 'green'
                self.canvas.create_rectangle(dx1, dy1, dx2, dy2, outline=color, width=2)
                
                # Add label
                self.canvas.create_text(dx1 + 5, dy1 + 5, text=f"{i+1}", fill=color, font=('Arial', 10, 'bold'))
    
    def start_selection(self, event):
        """Start region selection"""
        if self.original_image is None:
            return
        
        self.selection_start = (event.x, event.y)
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.selection_start is None:
            return
        
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
        
        self.selection_rect = self.canvas.create_rectangle(
            self.selection_start[0], self.selection_start[1],
            event.x, event.y,
            outline='red', width=2
        )
    
    def end_selection(self, event):
        """End region selection and store coordinates"""
        if self.selection_start is None:
            return
        
        self.selection_end = (event.x, event.y)
        
        # Convert display coordinates to original image coordinates
        x1 = int(min(self.selection_start[0], self.selection_end[0]) / self.scale_factor)
        y1 = int(min(self.selection_start[1], self.selection_end[1]) / self.scale_factor)
        x2 = int(max(self.selection_start[0], self.selection_end[0]) / self.scale_factor)
        y2 = int(max(self.selection_start[1], self.selection_end[1]) / self.scale_factor)
        
        # Ensure coordinates are within image bounds
        h, w = self.original_image.shape[:2]
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(0, min(x2, w))
        y2 = max(0, min(y2, h))
        
        if x2 - x1 > 10 and y2 - y1 > 10:  # Minimum size check
            self.selected_regions.append((x1, y1, x2, y2))
            self.status_var.set(f"Region {len(self.selected_regions)} selected: ({x1},{y1}) to ({x2},{y2})")
            self.redraw_canvas_with_regions()
        
        self.selection_start = None
        self.selection_end = None
    
    def clear_selections(self):
        """Clear all selected regions"""
        self.selected_regions = []
        if self.canvas:
            self.canvas.delete("all")
            if self.display_image:
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
        self.status_var.set("Selections cleared")
    
    def preprocess_image(self, image, method='card_optimized'):
        """Advanced preprocessing for card recognition"""
        if method == 'original':
            return image
        
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        if method == 'grayscale':
            return gray
        
        elif method == 'enhanced':
            # Apply CLAHE for better contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Apply bilateral filter
            filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            # Adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            return thresh
        
        elif method == 'card_optimized':
            # Specialized preprocessing for playing cards
            
            # Resize if too small (cards need to be readable)
            h, w = gray.shape
            if h < 50 or w < 30:
                scale = max(50/h, 30/w)
                new_h, new_w = int(h * scale), int(w * scale)
                gray = cv2.resize(gray, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
            
            # Apply CLAHE for better contrast
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4,4))
            enhanced = clahe.apply(gray)
            
            # Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(enhanced, (3, 3), 0)
            
            # Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            cleaned = cv2.morphologyEx(blurred, cv2.MORPH_CLOSE, kernel)
            
            # Adaptive thresholding optimized for cards
            thresh = cv2.adaptiveThreshold(
                cleaned, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 9, 3
            )
            
            # Invert if background is dark
            if np.mean(thresh) < 127:
                thresh = cv2.bitwise_not(thresh)
            
            return thresh
        
        return gray
    
    def run_ocr_with_engine(self, engine_name, image):
        """Run OCR with a specific engine"""
        try:
            if engine_name == 'ddddocr':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    _, buffer = cv2.imencode('.png', cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                else:
                    _, buffer = cv2.imencode('.png', image)
                result = self.ocr_engines['ddddocr'].classification(buffer.tobytes())
                return result.strip()
            
            elif engine_name == 'paddleocr':
                result = self.ocr_engines['paddleocr'].ocr(image, cls=True)
                if result and result[0]:
                    text_parts = []
                    for line in result[0]:
                        if len(line) > 1:
                            text_parts.append(line[1][0])
                    return ' '.join(text_parts).strip()
                return ""
            
            elif engine_name == 'tesseract':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(image)
                else:
                    pil_image = Image.fromarray(image)
                
                # Card-optimized Tesseract configs
                configs = [
                    '--psm 8 -c tessedit_char_whitelist=AKQJ23456789♠♥♦♣',  # Cards only
                    '--psm 10 -c tessedit_char_whitelist=AKQJ23456789',      # Single character
                    '--psm 8',   # Single word
                    '--psm 7'    # Single line
                ]
                
                for config in configs:
                    try:
                        result = pytesseract.image_to_string(pil_image, config=config).strip()
                        if result and len(result) <= 3:  # Cards should be short
                            return result
                    except:
                        continue
                
                return ""
            
        except Exception as e:
            return f"Error: {str(e)}"
        
        return ""
    
    def run_ocr_comparison(self):
        """Run OCR comparison on all selected regions"""
        if not self.selected_regions:
            messagebox.showwarning("Warning", "Please select regions first (try 'Suggest Card Regions')")
            return
        
        if not self.ocr_engines:
            messagebox.showerror("Error", "No OCR engines available")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "🔍 Running Card OCR Analysis...\n")
        self.results_text.insert(tk.END, "=" * 70 + "\n\n")
        
        # Run OCR in a separate thread
        threading.Thread(target=self._run_ocr_thread, daemon=True).start()
    
    def _run_ocr_thread(self):
        """Thread function for running OCR"""
        try:
            all_results = []
            preprocessing_method = self.preprocessing_var.get()
            
            for i, (x1, y1, x2, y2) in enumerate(self.selected_regions):
                self.root.after(0, lambda i=i: self.status_var.set(f"Processing region {i+1}/{len(self.selected_regions)}..."))
                
                # Extract region
                region = self.original_image[y1:y2, x1:x2]
                
                # Save original region
                cv2.imwrite(f"card_region_{i+1}_original.png", region)
                
                # Apply preprocessing
                processed_region = self.preprocess_image(region, preprocessing_method)
                cv2.imwrite(f"card_region_{i+1}_{preprocessing_method}.png", processed_region)
                
                # Test OCR engines
                results = {}
                
                for engine_name in self.ocr_engines:
                    start_time = time.time()
                    result = self.run_ocr_with_engine(engine_name, processed_region)
                    end_time = time.time()
                    
                    results[engine_name] = {
                        'text': result,
                        'time': end_time - start_time
                    }
                
                all_results.append({
                    'region': (x1, y1, x2, y2),
                    'results': results
                })
            
            # Display results
            self.root.after(0, lambda: self._display_card_results(all_results, preprocessing_method))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"OCR processing failed: {str(e)}"))
    
    def _display_card_results(self, all_results, preprocessing_method):
        """Display card OCR results with analysis"""
        self.results_text.delete(1.0, tk.END)
        
        # Header
        self.results_text.insert(tk.END, f"🃏 Card OCR Analysis Results (Preprocessing: {preprocessing_method})\n")
        self.results_text.insert(tk.END, "=" * 70 + "\n\n")
        
        detected_cards = []
        
        for i, region_data in enumerate(all_results):
            x1, y1, x2, y2 = region_data['region']
            results = region_data['results']
            
            # Determine hand position
            w = self.original_image.shape[1]
            if x1 < w * 0.33:
                hand = "LEFT"
            elif x1 < w * 0.66:
                hand = "CENTER"
            else:
                hand = "RIGHT"
            
            self.results_text.insert(tk.END, f"🎯 Region {i+1} ({hand} HAND): ({x1},{y1}) to ({x2},{y2})\n")
            self.results_text.insert(tk.END, "-" * 50 + "\n")
            
            # Display results for each engine
            valid_results = []
            for engine_name, data in results.items():
                text = data['text']
                time_taken = data['time']
                
                # Filter for likely card values
                if text and not text.startswith('Error'):
                    # Check if it looks like a card
                    card_chars = set('AKQJ23456789♠♥♦♣')
                    if any(c in card_chars for c in text.upper()) and len(text) <= 3:
                        valid_results.append(text)
                
                self.results_text.insert(tk.END, f"  {engine_name:12s}: '{text}' ({time_taken:.3f}s)\n")
            
            # Find consensus
            if valid_results:
                text_counts = Counter(valid_results)
                best_text = text_counts.most_common(1)[0][0]
                confidence = text_counts.most_common(1)[0][1] / len(valid_results)
                
                self.results_text.insert(tk.END, f"\n🏆 DETECTED CARD: '{best_text}' (Confidence: {confidence:.1%})\n")
                detected_cards.append(f"{hand}:{best_text}")
            else:
                self.results_text.insert(tk.END, f"\n❌ No valid card detected\n")
                detected_cards.append(f"{hand}:Unknown")
            
            self.results_text.insert(tk.END, "\n" + "=" * 70 + "\n\n")
        
        # Summary
        self.results_text.insert(tk.END, "📊 CARD DETECTION SUMMARY:\n")
        self.results_text.insert(tk.END, "-" * 30 + "\n")
        
        hands = {"LEFT": [], "CENTER": [], "RIGHT": []}
        for card_info in detected_cards:
            hand, card = card_info.split(":")
            hands[hand].append(card)
        
        for hand, cards in hands.items():
            cards_str = ", ".join(cards) if cards else "None detected"
            self.results_text.insert(tk.END, f"{hand:8s} Hand: {cards_str}\n")
        
        self.results_text.insert(tk.END, f"\n💾 Card images saved as: card_region_1_original.png, card_region_1_{preprocessing_method}.png, etc.\n")
        
        self.status_var.set("Card OCR analysis completed")
    
    def run(self):
        """Start the application"""
        # Load default image if provided
        default_image = r"D:\Machine Learning Journey\Vscode Projects\Eziline Projects\Client\testing\Screenshot 2025-06-05 160750.png"
        if os.path.exists(default_image):
            self.load_image(default_image)
        
        self.root.mainloop()

if __name__ == "__main__":
    app = EnhancedCardOCR()
    app.run()
