"""
Quick OCR Test Script
Simple command-line tool to test multiple OCR engines on image regions
"""

import cv2
import numpy as np
import os
import time
from collections import Counter

# OCR Engine imports
try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
except ImportError:
    DDDDOCR_AVAILABLE = False
    print("⚠️ ddddocr not available")

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("⚠️ PaddleOCR not available")

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("⚠️ EasyOCR not available")

try:
    import pytesseract
    from PIL import Image
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("⚠️ Tesseract not available")

class QuickOCRTest:
    def __init__(self):
        self.ocr_engines = {}
        self.init_ocr_engines()
    
    def init_ocr_engines(self):
        """Initialize available OCR engines"""
        print("🔧 Initializing OCR engines...")
        
        if DDDDOCR_AVAILABLE:
            try:
                self.ocr_engines['ddddocr'] = ddddocr.DdddOcr(show_ad=False)
                print("✅ ddddocr initialized")
            except Exception as e:
                print(f"❌ ddddocr failed: {e}")
        
        if PADDLEOCR_AVAILABLE:
            try:
                self.ocr_engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                print("✅ PaddleOCR initialized")
            except Exception as e:
                print(f"❌ PaddleOCR failed: {e}")
        
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['en'], gpu=False)
                print("✅ EasyOCR initialized")
            except Exception as e:
                print(f"❌ EasyOCR failed: {e}")
        
        if TESSERACT_AVAILABLE:
            try:
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = 'tesseract'
                print("✅ Tesseract initialized")
            except Exception as e:
                print(f"❌ Tesseract failed: {e}")
    
    def preprocess_image(self, image, method='grayscale'):
        """Preprocess image for better OCR results"""
        if method == 'grayscale':
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (1, 1), 0)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            return thresh
        
        elif method == 'enhanced':
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Apply bilateral filter to reduce noise while keeping edges sharp
            filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            return thresh
        
        return image
    
    def run_ocr_with_engine(self, engine_name, image):
        """Run OCR with a specific engine"""
        try:
            if engine_name == 'ddddocr':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    _, buffer = cv2.imencode('.png', cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                else:
                    _, buffer = cv2.imencode('.png', image)
                result = self.ocr_engines['ddddocr'].classification(buffer.tobytes())
                return result.strip()
            
            elif engine_name == 'paddleocr':
                result = self.ocr_engines['paddleocr'].ocr(image, cls=True)
                if result and result[0]:
                    text_parts = []
                    for line in result[0]:
                        if len(line) > 1:
                            text_parts.append(line[1][0])
                    return ' '.join(text_parts).strip()
                return ""
            
            elif engine_name == 'easyocr':
                result = self.ocr_engines['easyocr'].readtext(image, detail=0)
                return ' '.join(result).strip()
            
            elif engine_name == 'tesseract':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(image)
                else:
                    pil_image = Image.fromarray(image)
                
                # Try different PSM modes
                configs = [
                    '--psm 8',  # Single word
                    '--psm 7',  # Single text line
                    '--psm 6',  # Single uniform block
                    '--psm 13'  # Raw line
                ]
                
                results = []
                for config in configs:
                    try:
                        result = pytesseract.image_to_string(pil_image, config=config).strip()
                        if result:
                            results.append(result)
                    except:
                        continue
                
                return results[0] if results else ""
            
        except Exception as e:
            return f"Error: {str(e)}"
        
        return ""
    
    def test_region(self, image, x1, y1, x2, y2):
        """Test OCR on a specific region"""
        print(f"\n🔍 Testing region: ({x1},{y1}) to ({x2},{y2})")
        print("-" * 60)
        
        # Extract region
        region = image[y1:y2, x1:x2]
        
        if region.size == 0:
            print("❌ Invalid region coordinates")
            return
        
        # Save region for inspection
        cv2.imwrite(f"region_{x1}_{y1}_{x2}_{y2}_original.png", region)
        
        # Test different preprocessing methods
        preprocessing_methods = ['original', 'grayscale', 'enhanced']
        all_results = {}
        
        for method in preprocessing_methods:
            if method == 'original':
                processed_region = region
            else:
                processed_region = self.preprocess_image(region, method)
                cv2.imwrite(f"region_{x1}_{y1}_{x2}_{y2}_{method}.png", processed_region)
            
            print(f"\n📊 {method.upper()} preprocessing:")
            
            for engine_name in self.ocr_engines:
                start_time = time.time()
                result = self.run_ocr_with_engine(engine_name, processed_region)
                end_time = time.time()
                
                key = f"{engine_name}_{method}"
                all_results[key] = result
                
                print(f"  {engine_name:12s}: '{result}' ({end_time-start_time:.3f}s)")
        
        # Find consensus result
        all_texts = [result for result in all_results.values() if result and not result.startswith('Error')]
        if all_texts:
            text_counts = Counter(all_texts)
            best_text, count = text_counts.most_common(1)[0]
            confidence = count / len(all_texts)
            
            print(f"\n🏆 BEST RESULT: '{best_text}' (Confidence: {confidence:.1%})")
            print(f"📊 Agreement: {count}/{len(all_texts)} engines")
        else:
            print("\n❌ No successful OCR results")
        
        return all_results
    
    def interactive_test(self, image_path):
        """Interactive testing with user-defined regions"""
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return
        
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return
        
        h, w = image.shape[:2]
        print(f"📷 Image loaded: {os.path.basename(image_path)} ({w}x{h})")
        print(f"🔧 Available OCR engines: {list(self.ocr_engines.keys())}")
        
        while True:
            print("\n" + "="*60)
            print("🎯 OCR Region Testing")
            print("="*60)
            print("Enter region coordinates (x1 y1 x2 y2) or 'q' to quit")
            print("Example: 100 50 200 100")
            print(f"Image size: {w}x{h}")
            
            user_input = input("\nCoordinates: ").strip()
            
            if user_input.lower() == 'q':
                break
            
            try:
                coords = list(map(int, user_input.split()))
                if len(coords) != 4:
                    print("❌ Please enter exactly 4 coordinates")
                    continue
                
                x1, y1, x2, y2 = coords
                
                # Validate coordinates
                if x1 >= x2 or y1 >= y2:
                    print("❌ Invalid coordinates: x1 < x2 and y1 < y2")
                    continue
                
                if x1 < 0 or y1 < 0 or x2 > w or y2 > h:
                    print(f"❌ Coordinates out of bounds. Max: {w}x{h}")
                    continue
                
                # Test the region
                self.test_region(image, x1, y1, x2, y2)
                
            except ValueError:
                print("❌ Invalid input. Please enter 4 integers separated by spaces")
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    # Default image path
    default_image = r"D:\Machine Learning Journey\Vscode Projects\Eziline Projects\Client\testing\Screenshot 2025-06-05 160750.png"
    
    print("🎯 Quick OCR Test Tool")
    print("="*50)
    
    # Initialize OCR tester
    tester = QuickOCRTest()
    
    if not tester.ocr_engines:
        print("❌ No OCR engines available. Please install at least one:")
        print("  pip install ddddocr paddleocr easyocr pytesseract")
        return
    
    # Check if default image exists
    if os.path.exists(default_image):
        print(f"📷 Using default image: {os.path.basename(default_image)}")
        tester.interactive_test(default_image)
    else:
        print("📁 Default image not found. Please provide image path:")
        image_path = input("Image path: ").strip()
        if image_path:
            tester.interactive_test(image_path)

if __name__ == "__main__":
    main()
