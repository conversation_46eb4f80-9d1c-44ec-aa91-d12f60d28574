"""
Optimized YOLO Training Script for Imbalanced Dataset
Targets minimum 80% accuracy for all classes using advanced techniques
"""

import os
import yaml
import torch
import numpy as np
from ultralytics import YOLO
from collections import Counter
import glob
import shutil
from pathlib import Path
import random
import cv2
from sklearn.model_selection import train_test_split
import albumentations as A
from albumentations.pytorch import ToTensorV2

class DatasetBalancer:
    def __init__(self, data_yaml_path):
        self.data_yaml_path = data_yaml_path
        with open(data_yaml_path, 'r') as f:
            self.data_config = yaml.safe_load(f)
        self.class_names = self.data_config['names']
        self.nc = self.data_config['nc']
        
    def analyze_class_distribution(self, labels_dir):
        """Analyze class distribution in dataset"""
        class_counts = Counter()
        label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                for line in f:
                    if line.strip():
                        class_id = int(line.strip().split()[0])
                        class_counts[class_id] += 1
        return class_counts
    
    def augment_underrepresented_classes(self, train_images_dir, train_labels_dir, target_min_samples=100):
        """Augment underrepresented classes using advanced augmentation"""
        class_counts = self.analyze_class_distribution(train_labels_dir)
        
        # Define augmentation pipeline for underrepresented classes
        heavy_augment = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
            A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.8),
            A.RandomGamma(gamma_limit=(80, 120), p=0.5),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.MotionBlur(blur_limit=3, p=0.3),
            A.RandomRotate90(p=0.3),
            A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=15, p=0.5),
        ])
        
        # Find underrepresented classes
        max_count = max(class_counts.values()) if class_counts else 0
        underrepresented = {class_id: count for class_id, count in class_counts.items() 
                          if count < target_min_samples}
        
        print(f"Augmenting {len(underrepresented)} underrepresented classes...")
        
        for class_id, current_count in underrepresented.items():
            needed_samples = target_min_samples - current_count
            class_name = self.class_names[class_id]
            print(f"Class {class_id} ({class_name}): {current_count} -> {target_min_samples} (+{needed_samples})")
            
            # Find images containing this class
            class_images = []
            for label_file in glob.glob(os.path.join(train_labels_dir, '*.txt')):
                with open(label_file, 'r') as f:
                    if any(int(line.strip().split()[0]) == class_id for line in f if line.strip()):
                        image_name = os.path.basename(label_file).replace('.txt', '')
                        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                            img_path = os.path.join(train_images_dir, image_name + ext)
                            if os.path.exists(img_path):
                                class_images.append((img_path, label_file))
                                break
            
            # Generate augmented samples
            for i in range(needed_samples):
                if not class_images:
                    continue
                    
                img_path, label_path = random.choice(class_images)
                
                # Load and augment image
                image = cv2.imread(img_path)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                augmented = heavy_augment(image=image)
                augmented_image = cv2.cvtColor(augmented['image'], cv2.COLOR_RGB2BGR)
                
                # Save augmented image and copy label
                base_name = os.path.splitext(os.path.basename(img_path))[0]
                aug_img_name = f"{base_name}_aug_{i}.jpg"
                aug_label_name = f"{base_name}_aug_{i}.txt"
                
                cv2.imwrite(os.path.join(train_images_dir, aug_img_name), augmented_image)
                shutil.copy2(label_path, os.path.join(train_labels_dir, aug_label_name))
    
    def create_balanced_validation_split(self, train_images_dir, train_labels_dir, 
                                       val_images_dir, val_labels_dir, val_ratio=0.2):
        """Create a balanced validation split"""
        # Ensure validation directories exist
        os.makedirs(val_images_dir, exist_ok=True)
        os.makedirs(val_labels_dir, exist_ok=True)
        
        # Group images by classes
        class_to_images = {i: [] for i in range(self.nc)}
        
        for label_file in glob.glob(os.path.join(train_labels_dir, '*.txt')):
            with open(label_file, 'r') as f:
                classes_in_image = set()
                for line in f:
                    if line.strip():
                        class_id = int(line.strip().split()[0])
                        classes_in_image.add(class_id)
                
                image_name = os.path.basename(label_file).replace('.txt', '')
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    img_path = os.path.join(train_images_dir, image_name + ext)
                    if os.path.exists(img_path):
                        for class_id in classes_in_image:
                            class_to_images[class_id].append((img_path, label_file))
                        break
        
        # Select validation samples ensuring each class is represented
        val_samples = set()
        for class_id, images in class_to_images.items():
            if images:
                n_val = max(1, int(len(images) * val_ratio))
                selected = random.sample(images, min(n_val, len(images)))
                val_samples.update(selected)
        
        # Move selected samples to validation set
        for img_path, label_path in val_samples:
            img_name = os.path.basename(img_path)
            label_name = os.path.basename(label_path)
            
            shutil.move(img_path, os.path.join(val_images_dir, img_name))
            shutil.move(label_path, os.path.join(val_labels_dir, label_name))
        
        print(f"Created validation set with {len(val_samples)} samples")

def calculate_class_weights(labels_dir, nc):
    """Calculate class weights for focal loss"""
    class_counts = Counter()
    total_samples = 0
    
    for label_file in glob.glob(os.path.join(labels_dir, '*.txt')):
        with open(label_file, 'r') as f:
            for line in f:
                if line.strip():
                    class_id = int(line.strip().split()[0])
                    class_counts[class_id] += 1
                    total_samples += 1
    
    # Calculate inverse frequency weights
    weights = []
    for i in range(nc):
        count = class_counts.get(i, 1)  # Avoid division by zero
        weight = total_samples / (nc * count)
        weights.append(weight)
    
    return weights

def train_optimized_yolo(data_yaml_path, epochs=300, patience=50, target_accuracy=0.8):
    """Train YOLO with optimizations for high accuracy and class balance"""
    
    print("🚀 Starting Optimized YOLO Training")
    print("=" * 50)
    
    # Initialize dataset balancer
    balancer = DatasetBalancer(data_yaml_path)
    
    # Get paths
    with open(data_yaml_path, 'r') as f:
        data_config = yaml.safe_load(f)
    
    train_images_dir = data_config['train'].replace('/images', '/images')
    train_labels_dir = data_config['train'].replace('/images', '/labels')
    val_images_dir = data_config['val'].replace('/images', '/images')
    val_labels_dir = data_config['val'].replace('/images', '/labels')
    
    # Step 1: Augment underrepresented classes
    print("📈 Augmenting underrepresented classes...")
    balancer.augment_underrepresented_classes(train_images_dir, train_labels_dir, target_min_samples=80)
    
    # Step 2: Create better validation split if current one is too small
    current_val_count = len(glob.glob(os.path.join(val_images_dir, '*')))
    if current_val_count < 50:  # If validation set is too small
        print("🔄 Creating balanced validation split...")
        balancer.create_balanced_validation_split(train_images_dir, train_labels_dir,
                                                val_images_dir, val_labels_dir, val_ratio=0.15)
    
    # Step 3: Calculate class weights
    print("⚖️ Calculating class weights...")
    class_weights = calculate_class_weights(train_labels_dir, data_config['nc'])
    
    # Step 4: Initialize model with best practices
    print("🤖 Initializing YOLO model...")
    model = YOLO('yolo11n.pt')  # Start with YOLOv11 nano for efficiency
    
    # Step 5: Train with optimized hyperparameters
    print("🎯 Starting training with optimized hyperparameters...")
    
    results = model.train(
        data=data_yaml_path,
        epochs=epochs,
        patience=patience,
        batch=16,  # Adjust based on GPU memory
        imgsz=640,
        save=True,
        save_period=10,
        cache=True,
        device='0' if torch.cuda.is_available() else 'cpu',
        workers=4,
        project='runs/train',
        name='optimized_model',
        exist_ok=True,
        pretrained=True,
        optimizer='AdamW',
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        cos_lr=True,
        close_mosaic=10,
        resume=False,
        amp=True,
        fraction=1.0,
        profile=False,
        freeze=None,
        # Advanced hyperparameters for better accuracy
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        box=7.5,
        cls=0.5,
        dfl=1.5,
        pose=12.0,
        kobj=2.0,
        label_smoothing=0.0,
        nbs=64,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.0,
        translate=0.1,
        scale=0.5,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=1.0,
        mixup=0.0,
        copy_paste=0.0
    )
    
    print("✅ Training completed!")
    print(f"📊 Best model saved at: {model.trainer.best}")
    
    # Step 6: Validate and analyze results
    print("🔍 Validating model performance...")
    metrics = model.val()
    
    # Print per-class results
    if hasattr(metrics, 'results_dict'):
        print("\n📈 Per-class Performance:")
        print("-" * 40)
        for i, class_name in enumerate(data_config['names']):
            if i < len(metrics.ap_class_index):
                ap = metrics.ap[i] if hasattr(metrics, 'ap') else 0
                print(f"{class_name}: {ap:.3f} AP")
    
    return model, results

if __name__ == "__main__":
    # Set random seeds for reproducibility
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # Start training
    model, results = train_optimized_yolo(
        data_yaml_path="First.v2i.yolov8/data.yaml",
        epochs=300,
        patience=50,
        target_accuracy=0.8
    )
    
    print("\n🎉 Training pipeline completed!")
    print("Check the 'runs/train/optimized_model' directory for results.")
