"""
Advanced Hyperparameter Tuning for Maximum Accuracy
Use this after initial training to fine-tune for 80%+ accuracy on all classes
"""

import os
import yaml
from ultralytics import YOLO
import optuna
import torch
import numpy as np
from pathlib import Path

class YOLOHyperparameterTuner:
    def __init__(self, data_yaml_path, base_model_path=None):
        self.data_yaml_path = data_yaml_path
        self.base_model_path = base_model_path or 'yolo11n.pt'
        self.best_score = 0
        
    def objective(self, trial):
        """Optuna objective function for hyperparameter optimization"""
        
        # Suggest hyperparameters
        lr0 = trial.suggest_float('lr0', 0.001, 0.1, log=True)
        lrf = trial.suggest_float('lrf', 0.01, 0.2)
        momentum = trial.suggest_float('momentum', 0.8, 0.99)
        weight_decay = trial.suggest_float('weight_decay', 0.0001, 0.001, log=True)
        warmup_epochs = trial.suggest_float('warmup_epochs', 1.0, 5.0)
        box = trial.suggest_float('box', 5.0, 10.0)
        cls = trial.suggest_float('cls', 0.3, 1.0)
        dfl = trial.suggest_float('dfl', 1.0, 2.0)
        hsv_h = trial.suggest_float('hsv_h', 0.01, 0.02)
        hsv_s = trial.suggest_float('hsv_s', 0.5, 0.9)
        hsv_v = trial.suggest_float('hsv_v', 0.3, 0.6)
        translate = trial.suggest_float('translate', 0.05, 0.2)
        scale = trial.suggest_float('scale', 0.3, 0.7)
        fliplr = trial.suggest_float('fliplr', 0.3, 0.7)
        mosaic = trial.suggest_float('mosaic', 0.8, 1.0)
        
        # Initialize model
        model = YOLO(self.base_model_path)
        
        # Train with suggested hyperparameters
        try:
            results = model.train(
                data=self.data_yaml_path,
                epochs=100,  # Shorter epochs for tuning
                patience=20,
                batch=16,
                imgsz=640,
                save=False,  # Don't save intermediate models
                cache=True,
                device='0' if torch.cuda.is_available() else 'cpu',
                workers=4,
                project='runs/tune',
                name=f'trial_{trial.number}',
                exist_ok=True,
                verbose=False,
                # Hyperparameters to tune
                lr0=lr0,
                lrf=lrf,
                momentum=momentum,
                weight_decay=weight_decay,
                warmup_epochs=warmup_epochs,
                box=box,
                cls=cls,
                dfl=dfl,
                hsv_h=hsv_h,
                hsv_s=hsv_s,
                hsv_v=hsv_v,
                translate=translate,
                scale=scale,
                fliplr=fliplr,
                mosaic=mosaic,
            )
            
            # Get validation metrics
            metrics = model.val()
            
            # Calculate mean AP across all classes
            if hasattr(metrics, 'box') and hasattr(metrics.box, 'map'):
                map_score = metrics.box.map
            else:
                map_score = 0.0
                
            return map_score
            
        except Exception as e:
            print(f"Trial {trial.number} failed: {e}")
            return 0.0
    
    def tune_hyperparameters(self, n_trials=50):
        """Run hyperparameter tuning"""
        print(f"🔧 Starting hyperparameter tuning with {n_trials} trials...")
        
        study = optuna.create_study(direction='maximize')
        study.optimize(self.objective, n_trials=n_trials)
        
        print("🏆 Best hyperparameters found:")
        for key, value in study.best_params.items():
            print(f"  {key}: {value}")
        
        print(f"🎯 Best mAP score: {study.best_value:.4f}")
        
        return study.best_params

def train_with_best_params(data_yaml_path, best_params, epochs=500):
    """Train final model with best hyperparameters"""
    print("🚀 Training final model with optimized hyperparameters...")
    
    model = YOLO('yolo11s.pt')  # Use slightly larger model for final training
    
    results = model.train(
        data=data_yaml_path,
        epochs=epochs,
        patience=100,
        batch=16,
        imgsz=640,
        save=True,
        save_period=25,
        cache=True,
        device='0' if torch.cuda.is_available() else 'cpu',
        workers=4,
        project='runs/train',
        name='final_optimized_model',
        exist_ok=True,
        pretrained=True,
        optimizer='AdamW',
        verbose=True,
        seed=42,
        deterministic=True,
        cos_lr=True,
        close_mosaic=15,
        amp=True,
        # Apply best hyperparameters
        **best_params
    )
    
    return model, results

def analyze_per_class_performance(model, data_yaml_path):
    """Analyze per-class performance and identify weak classes"""
    print("📊 Analyzing per-class performance...")
    
    # Load class names
    with open(data_yaml_path, 'r') as f:
        data_config = yaml.safe_load(f)
    class_names = data_config['names']
    
    # Validate model
    metrics = model.val()
    
    # Print detailed per-class results
    print("\n📈 Detailed Per-Class Performance:")
    print("-" * 60)
    print(f"{'Class':<15} {'AP@0.5':<10} {'AP@0.5:0.95':<12} {'Status':<10}")
    print("-" * 60)
    
    weak_classes = []
    
    if hasattr(metrics, 'ap_class_index') and hasattr(metrics, 'ap'):
        for i, class_idx in enumerate(metrics.ap_class_index):
            if class_idx < len(class_names):
                class_name = class_names[class_idx]
                ap50 = metrics.ap50[i] if hasattr(metrics, 'ap50') else 0
                ap = metrics.ap[i] if hasattr(metrics, 'ap') else 0
                
                status = "✅ Good" if ap50 > 0.8 else "⚠️ Weak" if ap50 > 0.6 else "❌ Poor"
                if ap50 < 0.8:
                    weak_classes.append((class_name, ap50))
                
                print(f"{class_name:<15} {ap50:<10.3f} {ap:<12.3f} {status:<10}")
    
    if weak_classes:
        print(f"\n⚠️ Classes below 80% accuracy:")
        for class_name, ap50 in weak_classes:
            print(f"  - {class_name}: {ap50:.1%}")
        
        print("\n💡 Suggestions for improvement:")
        print("  1. Collect more data for weak classes")
        print("  2. Apply class-specific augmentation")
        print("  3. Adjust class weights in loss function")
        print("  4. Use ensemble methods")
    else:
        print("\n🎉 All classes achieved 80%+ accuracy!")
    
    return weak_classes

def main():
    data_yaml_path = "First.v2i.yolov8/data.yaml"
    
    print("🎯 Advanced YOLO Hyperparameter Tuning")
    print("=" * 50)
    
    # Check if we have a pre-trained model from initial training
    initial_model_path = "runs/train/optimized_model/weights/best.pt"
    if os.path.exists(initial_model_path):
        print(f"📁 Found initial trained model: {initial_model_path}")
        base_model = initial_model_path
    else:
        print("📁 Using pretrained YOLO model")
        base_model = 'yolo11n.pt'
    
    # Initialize tuner
    tuner = YOLOHyperparameterTuner(data_yaml_path, base_model)
    
    # Run hyperparameter tuning
    best_params = tuner.tune_hyperparameters(n_trials=30)
    
    # Train final model with best parameters
    final_model, results = train_with_best_params(data_yaml_path, best_params, epochs=500)
    
    # Analyze performance
    weak_classes = analyze_per_class_performance(final_model, data_yaml_path)
    
    print("\n🎉 Advanced training completed!")
    print("📁 Check 'runs/train/final_optimized_model' for the best model")

if __name__ == "__main__":
    main()
