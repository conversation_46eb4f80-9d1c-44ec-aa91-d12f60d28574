import os
from collections import Counter
import glob

def analyze_class_distribution(labels_dir):
    class_counts = Counter()
    label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
    
    for label_file in label_files:
        with open(label_file, 'r') as f:
            for line in f:
                if line.strip():
                    class_id = int(line.strip().split()[0])
                    class_counts[class_id] += 1
    
    return class_counts

# Class names from data.yaml
class_names = ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 
               'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 
               'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']

# Analyze train and validation sets
train_counts = analyze_class_distribution('First.v2i.yolov8/train/labels')
val_counts = analyze_class_distribution('First.v2i.yolov8/valid/labels')

print('Training set class distribution:')
for class_id, count in sorted(train_counts.items()):
    class_name = class_names[class_id] if class_id < len(class_names) else f'Unknown_{class_id}'
    print(f'Class {class_id} ({class_name}): {count} instances')

print('\nValidation set class distribution:')
for class_id, count in sorted(val_counts.items()):
    class_name = class_names[class_id] if class_id < len(class_names) else f'Unknown_{class_id}'
    print(f'Class {class_id} ({class_name}): {count} instances')

print(f'\nTotal training images: {len(glob.glob("First.v2i.yolov8/train/images/*"))}')
print(f'Total validation images: {len(glob.glob("First.v2i.yolov8/valid/images/*"))}')

# Calculate imbalance ratio
if train_counts:
    max_count = max(train_counts.values())
    min_count = min(train_counts.values())
    print(f'\nClass imbalance ratio: {max_count/min_count:.2f}:1')
    
    # Identify underrepresented classes (less than 10% of max class)
    threshold = max_count * 0.1
    underrepresented = [class_id for class_id, count in train_counts.items() if count < threshold]
    if underrepresented:
        print(f'Underrepresented classes (< 10% of max): {underrepresented}')
