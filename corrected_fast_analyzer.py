import os
import cv2
import numpy as np
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import threading
from ultralytics import YOLO
import easyocr
import sqlite3
import json
import re

# === CONFIG ===
TEST_IMAGE_PATH = "testing/Screenshot 2025-06-05 160750.png"
CARD_MODEL_PATH = "detect_yolo_one.pt"  # For capturing suits and numbers
WINNING_SEAT_MODEL_PATH = "capture_m.pt"  # For winning seat detection
DB_PATH = "game_analysis.db"

# Teen Patti game area coordinates
CROP_BOX = (1173, 390, 1918, 1227)

# Expected results for validation
EXPECTED_RESULTS = {
    'left_hand': {'cards': ['4', '6', 'K'], 'type': 'High Card'},
    'center_hand': {'cards': ['5', '8', '10'], 'type': 'High Card'},
    'right_hand': {'cards': ['2', '3', '4'], 'type': 'Sequence'}
}

class CorrectedFastAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("⚡ Corrected Fast Teen Patti Analyzer")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.models_loaded = False
        self.crop_box = CROP_BOX
        self.current_image = None
        self.full_screenshot = None
        self.analysis_results = {}
        self.screenshot_path = TEST_IMAGE_PATH
        
        # Hand regions (can be calibrated)
        self.hand_regions = {}
        self.regions_calibrated = False
        
        # Models
        self.card_model = None
        self.winning_seat_model = None
        self.easy_ocr = None
        
        # Card mappings
        self.card_values = {
            '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14
        }
        
        # Database
        self.setup_database()
        
        # Create GUI
        self.create_widgets()
        
        # Auto-load screenshot
        self.load_screenshot()
        
    def setup_database(self):
        """Setup database"""
        self.conn = sqlite3.connect(DB_PATH)
        self.cursor = self.conn.cursor()
        
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS corrected_fast_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                screenshot_path TEXT,
                
                -- Hand Analysis
                left_hand_cards TEXT,
                left_hand_type TEXT,
                left_hand_match BOOLEAN,
                
                center_hand_cards TEXT,
                center_hand_type TEXT,
                center_hand_match BOOLEAN,
                
                right_hand_cards TEXT,
                right_hand_type TEXT,
                right_hand_match BOOLEAN,
                
                -- Performance
                processing_time REAL,
                accuracy_score REAL,
                
                -- Winning
                winning_seat TEXT,
                winning_confidence REAL,
                
                -- Models Used
                card_model TEXT,
                winning_model TEXT
            )
        ''')
        self.conn.commit()
    
    def create_widgets(self):
        """Create GUI widgets"""
        
        # Title
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(title_frame, text="⚡ Corrected Fast Teen Patti Analyzer", 
                              font=('Arial', 18, 'bold'), fg='#00ff00', bg='#2b2b2b')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame, text="Using: detect_yolo_one.pt (suits/numbers/sequences) + capture_m.pt (winning seat)",
                                 font=('Arial', 12), fg='#cccccc', bg='#2b2b2b')
        subtitle_label.pack()
        
        # Expected results display
        expected_frame = tk.LabelFrame(self.root, text="🎯 Target Results", 
                                      font=('Arial', 11, 'bold'), fg='white', bg='#2b2b2b')
        expected_frame.pack(fill='x', padx=10, pady=5)
        
        expected_text = tk.Text(expected_frame, height=3, bg='#1e1e1e', fg='#00ff00',
                               font=('Consolas', 10))
        expected_text.pack(fill='x', padx=5, pady=5)
        expected_text.insert('1.0', 
            "🔵 Left: 4, 6, K (High Card)  |  🟣 Center: 5, 8, 10 (High Card)  |  🟡 Right: 2, 3, 4 (Sequence)")
        expected_text.config(state='disabled')
        
        # Control panel
        control_frame = tk.Frame(self.root, bg='#2b2b2b')
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.load_models_btn = tk.Button(control_frame, text="⚡ Load Models", 
                                        command=self.load_models, bg='#2196F3', fg='white',
                                        font=('Arial', 11, 'bold'), padx=15)
        self.load_models_btn.pack(side='left', padx=5)
        
        self.manual_calibrate_btn = tk.Button(control_frame, text="✋ Manual Calibrate", 
                                             command=self.manual_calibrate, bg='#E91E63', fg='white',
                                             font=('Arial', 11, 'bold'), padx=15)
        self.manual_calibrate_btn.pack(side='left', padx=5)
        
        self.view_regions_btn = tk.Button(control_frame, text="👁️ View Regions", 
                                         command=self.view_regions, bg='#607D8B', fg='white',
                                         font=('Arial', 11, 'bold'), padx=15)
        self.view_regions_btn.pack(side='left', padx=5)
        
        self.fast_analysis_btn = tk.Button(control_frame, text="⚡ Fast Analysis",
                                          command=self.fast_analysis, bg='#4CAF50', fg='white',
                                          font=('Arial', 11, 'bold'), padx=15, state='disabled')
        self.fast_analysis_btn.pack(side='left', padx=5)

        self.full_image_analysis_btn = tk.Button(control_frame, text="🖼️ Full Image Analysis",
                                                command=self.full_image_analysis, bg='#FF5722', fg='white',
                                                font=('Arial', 11, 'bold'), padx=15, state='disabled')
        self.full_image_analysis_btn.pack(side='left', padx=5)
        
        self.save_btn = tk.Button(control_frame, text="💾 Save", 
                                 command=self.save_results, bg='#9C27B0', fg='white',
                                 font=('Arial', 11, 'bold'), padx=15, state='disabled')
        self.save_btn.pack(side='left', padx=5)
        
        # Status
        status_frame = tk.Frame(self.root, bg='#2b2b2b')
        status_frame.pack(fill='x', padx=10, pady=2)
        
        tk.Label(status_frame, text="Status:", font=('Arial', 11, 'bold'), 
                fg='white', bg='#2b2b2b').pack(side='left')
        
        self.status_label = tk.Label(status_frame, text="Ready - Load corrected models", 
                                    font=('Arial', 11), fg='#ffff00', bg='#2b2b2b')
        self.status_label.pack(side='left', padx=10)
        
        # Regions status
        tk.Label(status_frame, text="Regions:", font=('Arial', 11, 'bold'), 
                fg='white', bg='#2b2b2b').pack(side='right', padx=(20,0))
        
        self.regions_status_label = tk.Label(status_frame, text="❌ Not Calibrated", 
                                            font=('Arial', 11), fg='#ff4444', bg='#2b2b2b')
        self.regions_status_label.pack(side='right', padx=10)
        
        # Main content area
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Left side - Game image
        image_frame = tk.LabelFrame(main_frame, text="📸 Teen Patti Game Area", 
                                   font=('Arial', 12, 'bold'), fg='white', bg='#2b2b2b')
        image_frame.pack(side='left', fill='both', expand=True, padx=5)
        
        self.image_label = tk.Label(image_frame, text="Game area loaded", 
                                   font=('Arial', 12), fg='gray', bg='#2b2b2b',
                                   width=50, height=25)
        self.image_label.pack(padx=10, pady=10)
        
        # Auto-display game area
        self.display_game_area()
        
        # Right side - Results
        results_frame = tk.LabelFrame(main_frame, text="⚡ Corrected Analysis Results", 
                                     font=('Arial', 12, 'bold'), fg='white', bg='#2b2b2b')
        results_frame.pack(side='right', fill='both', expand=True, padx=5)
        
        # Results table
        self.results_tree = ttk.Treeview(results_frame,
                                        columns=('Hand', 'Cards', 'Type', 'Expected', 'Match', 'Mode', 'Time'), show='headings', height=15)
        self.results_tree.heading('Hand', text='Hand')
        self.results_tree.heading('Cards', text='Cards Found')
        self.results_tree.heading('Type', text='Type')
        self.results_tree.heading('Expected', text='Expected')
        self.results_tree.heading('Match', text='Match?')
        self.results_tree.heading('Mode', text='Mode')
        self.results_tree.heading('Time', text='Time (ms)')

        # Set column widths
        self.results_tree.column('Hand', width=80)
        self.results_tree.column('Cards', width=100)
        self.results_tree.column('Type', width=80)
        self.results_tree.column('Expected', width=120)
        self.results_tree.column('Match', width=60)
        self.results_tree.column('Mode', width=70)
        self.results_tree.column('Time', width=70)
        
        self.results_tree.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Bottom - Activity log
        log_frame = tk.LabelFrame(self.root, text="📝 Corrected Analysis Log", 
                                 font=('Arial', 11, 'bold'), fg='white', bg='#2b2b2b')
        log_frame.pack(fill='x', padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, height=8, bg='#1e1e1e', fg='white',
                               font=('Consolas', 9))
        self.log_text.pack(fill='x', padx=5, pady=5)
        
        # Initial log
        self.log_message("⚡ Corrected Fast Teen Patti Analyzer initialized")
        self.log_message("🎯 Using correct models: detect_yolo_one.pt + capture_m.pt")
        self.log_message("🎯 Target: Left(4,6,K), Center(5,8,10), Right(2,3,4)")
    
    def load_screenshot(self):
        """Load screenshot"""
        try:
            if os.path.exists(self.screenshot_path):
                self.full_screenshot = cv2.imread(self.screenshot_path)
                
                if self.full_screenshot is None:
                    self.log_message(f"❌ Could not load screenshot")
                    return
                
                # Crop game area
                x1, y1, x2, y2 = self.crop_box
                self.current_image = self.full_screenshot[y1:y2, x1:x2]
                
                self.log_message(f"✅ Screenshot loaded: {self.current_image.shape}")
                
                # Initialize default regions
                self.initialize_default_regions()
                
            else:
                self.log_message(f"❌ Screenshot not found: {self.screenshot_path}")
                
        except Exception as e:
            self.log_message(f"❌ Error loading screenshot: {e}")
    
    def initialize_default_regions(self):
        """Initialize default hand regions"""
        if self.current_image is not None:
            height, width = self.current_image.shape[:2]
            self.hand_regions = {
                'left_hand': (0, 0, width//3, height),
                'center_hand': (width//3, 0, 2*width//3, height),
                'right_hand': (2*width//3, 0, width, height)
            }
    
    def display_game_area(self):
        """Display the cropped game area"""
        if self.current_image is None:
            return
        
        try:
            # Display cropped image
            img_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
            
            height, width = img_rgb.shape[:2]
            max_width, max_height = 600, 400
            
            if width > max_width or height > max_height:
                scale = min(max_width/width, max_height/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img_rgb = cv2.resize(img_rgb, (new_width, new_height))
            
            # Convert to PhotoImage
            img_pil = Image.fromarray(img_rgb)
            img_tk = ImageTk.PhotoImage(img_pil)
            
            # Update label
            self.image_label.config(image=img_tk, text="")
            self.image_label.image = img_tk  # Keep reference
            
        except Exception as e:
            self.log_message(f"❌ Display error: {e}")
    
    def log_message(self, message):
        """Add message to activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

    def load_models(self):
        """Load corrected models"""
        self.log_message("⚡ Loading corrected models...")
        self.status_label.config(text="Loading models...", fg='#ff8800')

        def load_in_thread():
            try:
                # Load card detection model (suits and numbers)
                self.card_model = YOLO(CARD_MODEL_PATH)
                self.log_message(f"✅ Card model loaded: {CARD_MODEL_PATH}")

                # Load winning seat model
                self.winning_seat_model = YOLO(WINNING_SEAT_MODEL_PATH)
                self.log_message(f"✅ Winning seat model loaded: {WINNING_SEAT_MODEL_PATH}")

                # Load EasyOCR for text extraction
                self.easy_ocr = easyocr.Reader(['en'])
                self.log_message("✅ EasyOCR loaded")

                self.models_loaded = True
                self.root.after(0, self.models_loaded_callback)

            except Exception as e:
                self.root.after(0, lambda: self.models_error_callback(str(e)))

        threading.Thread(target=load_in_thread, daemon=True).start()

    def models_loaded_callback(self):
        """Called when models are loaded"""
        self.status_label.config(text="Corrected models loaded - Ready for analysis", fg='#00ff00')
        self.fast_analysis_btn.config(state='normal')
        self.full_image_analysis_btn.config(state='normal')
        self.log_message("🎉 All corrected models loaded successfully!")

    def models_error_callback(self, error):
        """Called when model loading fails"""
        self.status_label.config(text="Model loading failed", fg='#ff0000')
        self.log_message(f"❌ Model loading error: {error}")
        messagebox.showerror("Model Error", f"Failed to load models:\n{error}")

    def manual_calibrate(self):
        """Manual calibration with interactive region selection"""
        if self.current_image is None:
            messagebox.showerror("Error", "No image loaded!")
            return

        self.log_message("✋ Starting manual calibration...")
        self.log_message("💡 You will select each hand region by clicking and dragging")

        # Calibrate each hand region
        for hand_name in ['left_hand', 'center_hand', 'right_hand']:
            expected = EXPECTED_RESULTS[hand_name]
            hand_display = hand_name.replace('_', ' ').title()

            self.log_message(f"📍 Calibrating {hand_display}")
            self.log_message(f"   Expected: {', '.join(expected['cards'])} ({expected['type']})")

            region = self.select_region_interactive(hand_name, hand_display, expected)
            if region:
                self.hand_regions[hand_name] = region
                self.log_message(f"✅ {hand_display} region set: {region}")
            else:
                self.log_message(f"❌ {hand_display} region skipped")

        # Update status
        self.regions_calibrated = True
        self.regions_status_label.config(text="✅ Manual Calibrated", fg='#00ff00')
        self.log_message("🎉 Manual calibration completed!")

    def select_region_interactive(self, hand_name, hand_display, expected):
        """Interactive region selection"""
        try:
            # Create instruction image
            img_with_instruction = self.current_image.copy()

            # Add instruction text
            instruction = f"Select {hand_display} region"
            expected_text = f"Expected: {', '.join(expected['cards'])} ({expected['type']})"
            controls_text = "Click and drag to select | 's' = save | 'r' = reset | 'q' = skip"

            cv2.putText(img_with_instruction, instruction, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(img_with_instruction, expected_text, (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 0), 2)
            cv2.putText(img_with_instruction, controls_text, (10, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Global variables for mouse callback
            global start_point, end_point, drawing, img_copy
            start_point = (0, 0)
            end_point = (0, 0)
            drawing = False
            img_copy = img_with_instruction.copy()

            def mouse_callback(event, x, y, flags, param):
                global start_point, end_point, drawing, img_copy

                if event == cv2.EVENT_LBUTTONDOWN:
                    drawing = True
                    start_point = (x, y)
                    end_point = start_point

                elif event == cv2.EVENT_MOUSEMOVE:
                    if drawing:
                        img_copy = img_with_instruction.copy()
                        end_point = (x, y)
                        cv2.rectangle(img_copy, start_point, end_point, (0, 255, 0), 2)

                        # Show region size
                        width = abs(end_point[0] - start_point[0])
                        height = abs(end_point[1] - start_point[1])
                        size_text = f"Size: {width}x{height}"
                        cv2.putText(img_copy, size_text, (10, 150),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                        cv2.imshow(f"Manual Calibration - {hand_display}", img_copy)

                elif event == cv2.EVENT_LBUTTONUP:
                    drawing = False
                    end_point = (x, y)
                    cv2.rectangle(img_copy, start_point, end_point, (0, 255, 0), 2)

                    # Show final region info
                    width = abs(end_point[0] - start_point[0])
                    height = abs(end_point[1] - start_point[1])
                    size_text = f"Size: {width}x{height} | Press 's' to save"
                    cv2.putText(img_copy, size_text, (10, 150),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

                    cv2.imshow(f"Manual Calibration - {hand_display}", img_copy)

            # Create window and set mouse callback
            window_name = f"Manual Calibration - {hand_display}"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_name, 800, 600)
            cv2.setMouseCallback(window_name, mouse_callback)
            cv2.imshow(window_name, img_copy)

            self.log_message(f"🖱️ Select {hand_display} region:")
            self.log_message("   • Click and drag to draw rectangle")
            self.log_message("   • Press 's' to save region")
            self.log_message("   • Press 'r' to reset selection")
            self.log_message("   • Press 'q' to skip this hand")

            while True:
                key = cv2.waitKey(1) & 0xFF

                if key == ord('s'):  # Save
                    if start_point != end_point:
                        x1, y1 = min(start_point[0], end_point[0]), min(start_point[1], end_point[1])
                        x2, y2 = max(start_point[0], end_point[0]), max(start_point[1], end_point[1])

                        # Validate region size
                        width = x2 - x1
                        height = y2 - y1
                        if width < 20 or height < 20:
                            self.log_message("⚠️ Region too small! Please select a larger area.")
                            continue

                        cv2.destroyAllWindows()
                        self.log_message(f"✅ Region saved: ({x1}, {y1}, {x2}, {y2}) - {width}x{height}")
                        return (x1, y1, x2, y2)
                    else:
                        self.log_message("⚠️ Please select a region first!")

                elif key == ord('r'):  # Reset
                    img_copy = img_with_instruction.copy()
                    start_point = (0, 0)
                    end_point = (0, 0)
                    cv2.imshow(window_name, img_copy)
                    self.log_message("🔄 Selection reset - draw new region")

                elif key == ord('q'):  # Skip
                    cv2.destroyAllWindows()
                    self.log_message(f"⏭️ {hand_display} region skipped")
                    return None

                elif key == 27:  # ESC key
                    cv2.destroyAllWindows()
                    self.log_message(f"❌ Calibration cancelled")
                    return None

        except Exception as e:
            self.log_message(f"❌ Error in manual calibration: {e}")
            cv2.destroyAllWindows()
            return None

    def view_regions(self):
        """View current hand regions"""
        if self.current_image is None:
            messagebox.showerror("Error", "No image loaded!")
            return

        try:
            img_with_regions = self.current_image.copy()

            colors = {
                'left_hand': (255, 0, 0),    # Blue
                'center_hand': (255, 0, 255), # Purple
                'right_hand': (0, 255, 255)   # Yellow
            }

            for hand_name, region in self.hand_regions.items():
                if region:
                    x1, y1, x2, y2 = region
                    color = colors[hand_name]

                    # Draw rectangle
                    cv2.rectangle(img_with_regions, (x1, y1), (x2, y2), color, 3)

                    # Add label
                    label = hand_name.replace('_', ' ').title()
                    cv2.putText(img_with_regions, label, (x1 + 10, y1 + 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)

                    # Add expected cards
                    expected = EXPECTED_RESULTS[hand_name]
                    expected_text = f"Expected: {', '.join(expected['cards'])}"
                    cv2.putText(img_with_regions, expected_text, (x1 + 10, y1 + 60),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            # Add calibration status
            status_text = "✅ Calibrated" if self.regions_calibrated else "❌ Not Calibrated"
            cv2.putText(img_with_regions, f"Regions: {status_text}", (10, img_with_regions.shape[0] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            cv2.imshow("Hand Regions", img_with_regions)
            self.log_message("👁️ Viewing regions - Press any key to close")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        except Exception as e:
            self.log_message(f"❌ Error viewing regions: {e}")

    def fast_analysis(self):
        """Fast analysis with corrected models"""
        if not self.models_loaded:
            messagebox.showerror("Error", "Please load models first!")
            return

        if not self.regions_calibrated:
            messagebox.showerror("Error", "Please calibrate regions first!")
            return

        self.log_message("⚡ Starting fast analysis with corrected models...")
        self.status_label.config(text="Running corrected analysis...", fg='#ff8800')

        def analyze_in_thread():
            try:
                results = self.perform_corrected_analysis()
                self.root.after(0, lambda r=results: self.analysis_completed(r))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda err=error_msg: self.analysis_error(err))

        threading.Thread(target=analyze_in_thread, daemon=True).start()

    def full_image_analysis(self):
        """Full image analysis without manual regions"""
        if not self.models_loaded:
            messagebox.showerror("Error", "Please load models first!")
            return

        self.log_message("🖼️ Starting full image analysis (no manual regions)...")
        self.status_label.config(text="Running full image analysis...", fg='#ff8800')

        def analyze_in_thread():
            try:
                results = self.perform_full_image_analysis()
                self.root.after(0, lambda r=results: self.analysis_completed(r))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda err=error_msg: self.analysis_error(err))

        threading.Thread(target=analyze_in_thread, daemon=True).start()

    def perform_full_image_analysis(self):
        """Perform analysis on full image without regions"""
        import time
        start_time = time.time()

        results = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'screenshot_path': self.screenshot_path,
            'analysis_type': 'full_image',
            'hands': {},
            'models_used': {
                'card_model': CARD_MODEL_PATH,
                'winning_model': WINNING_SEAT_MODEL_PATH
            }
        }

        self.log_message("🔍 Analyzing full image with YOLO...")

        # Run YOLO on full image
        full_image_results = self.detect_cards_and_sequences_yolo(self.current_image)

        self.log_message(f"   Total detections: {len(full_image_results['all_detections'])}")
        self.log_message(f"   Cards found: {full_image_results['cards']}")
        self.log_message(f"   Sequences found: {full_image_results['sequences']}")

        # Group detections by spatial location (auto-detect hand regions)
        auto_regions = self.auto_detect_hand_regions(full_image_results['all_detections'])

        # Analyze each auto-detected region
        for region_name, detections in auto_regions.items():
            hand_start = time.time()

            self.log_message(f"🔍 Analyzing auto-detected {region_name}...")

            # Extract cards and sequences from this region
            region_cards = [d['label'] for d in detections if d.get('type') == 'value']
            region_sequences = [d['label'] for d in detections if d.get('type') == 'sequence']

            # Run OCR on the region area if we have bounding boxes
            if detections:
                # Calculate region bounds
                x_coords = []
                y_coords = []
                for detection in detections:
                    if 'bbox' in detection and detection['bbox']:
                        try:
                            x1, y1, x2, y2 = detection['bbox']
                            x_coords.extend([float(x1), float(x2)])
                            y_coords.extend([float(y1), float(y2)])
                        except (ValueError, TypeError, IndexError):
                            continue

                if x_coords and y_coords:
                    # Expand region slightly
                    margin = 20
                    region_x1 = max(0, int(min(x_coords) - margin))
                    region_y1 = max(0, int(min(y_coords) - margin))
                    region_x2 = min(self.current_image.shape[1], int(max(x_coords) + margin))
                    region_y2 = min(self.current_image.shape[0], int(max(y_coords) + margin))

                    # Ensure valid slice indices
                    if region_x2 > region_x1 and region_y2 > region_y1:
                        region_image = self.current_image[region_y1:region_y2, region_x1:region_x2]
                        ocr_detections = self.extract_text_ocr(region_image)
                    else:
                        ocr_detections = []
                else:
                    ocr_detections = []
            else:
                ocr_detections = []

            # Combine detections
            combined_cards = self.combine_detections(region_cards, ocr_detections)

            # Determine hand type
            hand_type = self.determine_hand_type_with_yolo(combined_cards, region_sequences)

            # Check match with expected results
            expected_hands = ['left_hand', 'center_hand', 'right_hand']
            if region_name in expected_hands:
                expected = EXPECTED_RESULTS[region_name]
                cards_match = set(combined_cards) == set(expected['cards'])
                type_match = hand_type == expected['type']
                overall_match = cards_match and type_match
            else:
                # For auto-detected regions, try to match with any expected result
                overall_match = False
                for exp_hand, exp_data in EXPECTED_RESULTS.items():
                    if (set(combined_cards) == set(exp_data['cards']) and
                        hand_type == exp_data['type']):
                        overall_match = True
                        break

            hand_time = (time.time() - hand_start) * 1000

            results['hands'][region_name] = {
                'cards': combined_cards,
                'type': hand_type,
                'match': overall_match,
                'yolo_cards': region_cards,
                'yolo_sequences': region_sequences,
                'ocr_detections': ocr_detections,
                'auto_detected': True,
                'detection_count': len(detections),
                'processing_time_ms': hand_time
            }

            self.log_message(f"   YOLO Cards: {region_cards}")
            self.log_message(f"   YOLO Sequences: {region_sequences}")
            self.log_message(f"   OCR: {ocr_detections}")
            self.log_message(f"   Final: {combined_cards} ({hand_type}) - {hand_time:.1f}ms")

        # Winning seat detection
        self.log_message("🏆 Detecting winning seat with capture_m.pt...")
        winning_info = self.detect_winning_seat_corrected()
        results['winning'] = winning_info

        results['total_processing_time'] = time.time() - start_time
        results['auto_regions'] = auto_regions

        return results

    def auto_detect_hand_regions(self, all_detections):
        """Auto-detect hand regions based on detection positions"""
        if not all_detections:
            return {}

        # Add bounding box info to detections if not present
        enhanced_detections = []
        for detection in all_detections:
            if 'bbox' not in detection:
                # If no bbox, we'll need to run YOLO again to get positions
                continue
            enhanced_detections.append(detection)

        if not enhanced_detections:
            # Fallback: run YOLO again to get bounding boxes
            try:
                results = self.card_model(self.current_image, verbose=False, conf=0.4)
                if results[0].boxes:
                    for i, box in enumerate(results[0].boxes):
                        if i < len(all_detections):
                            cls_id = int(box.cls[0])
                            label = self.card_model.names[cls_id]
                            confidence = float(box.conf[0])
                            xyxy = box.xyxy[0].cpu().numpy()

                            enhanced_detections.append({
                                'label': label,
                                'confidence': confidence,
                                'bbox': xyxy.tolist(),
                                'type': self.get_detection_type(label)
                            })
            except:
                pass

        if not enhanced_detections:
            return {'unknown_region': all_detections}

        # Group detections by X coordinate (left, center, right)
        image_width = self.current_image.shape[1]

        left_detections = []
        center_detections = []
        right_detections = []

        for detection in enhanced_detections:
            try:
                if 'bbox' in detection and detection['bbox']:
                    x1, y1, x2, y2 = detection['bbox']
                    x_center = (float(x1) + float(x2)) / 2

                    if x_center < image_width / 3:
                        left_detections.append(detection)
                    elif x_center < 2 * image_width / 3:
                        center_detections.append(detection)
                    else:
                        right_detections.append(detection)
            except (ValueError, TypeError, IndexError):
                # Skip invalid detections
                continue

        regions = {}
        if left_detections:
            regions['left_hand'] = left_detections
        if center_detections:
            regions['center_hand'] = center_detections
        if right_detections:
            regions['right_hand'] = right_detections

        self.log_message(f"🎯 Auto-detected regions: Left({len(left_detections)}), Center({len(center_detections)}), Right({len(right_detections)})")

        return regions

    def get_detection_type(self, label):
        """Get detection type for a label"""
        if label in self.card_values:
            return 'value'
        elif label in ['hearts', 'diamonds', 'clubs', 'spades']:
            return 'suit'
        elif label in ['sequence', 'straight']:
            return 'sequence'
        elif label in ['pair', 'color', 'flush', 'high_card']:
            return 'pattern'
        else:
            return 'other'

    def perform_corrected_analysis(self):
        """Perform analysis with corrected models"""
        import time
        start_time = time.time()

        results = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'screenshot_path': self.screenshot_path,
            'hands': {},
            'models_used': {
                'card_model': CARD_MODEL_PATH,
                'winning_model': WINNING_SEAT_MODEL_PATH
            }
        }

        # Analyze each hand region
        for hand_name in ['left_hand', 'center_hand', 'right_hand']:
            hand_start = time.time()

            # Extract region
            region = self.hand_regions.get(hand_name)
            if not region:
                continue

            x1, y1, x2, y2 = region
            hand_region = self.current_image[y1:y2, x1:x2]

            self.log_message(f"🔍 Analyzing {hand_name} with corrected models...")

            # Use YOLO to detect suits, numbers, and sequences
            yolo_results = self.detect_cards_and_sequences_yolo(hand_region)
            yolo_cards = yolo_results['cards']
            yolo_sequences = yolo_results['sequences']

            # Use OCR for additional text extraction
            ocr_detections = self.extract_text_ocr(hand_region)

            # Combine detections
            combined_cards = self.combine_detections(yolo_cards, ocr_detections)

            # Determine hand type (prioritize YOLO sequence detection)
            hand_type = self.determine_hand_type_with_yolo(combined_cards, yolo_sequences)

            # Check match
            expected = EXPECTED_RESULTS[hand_name]
            cards_match = set(combined_cards) == set(expected['cards'])
            type_match = hand_type == expected['type']
            overall_match = cards_match and type_match

            hand_time = (time.time() - hand_start) * 1000  # Convert to ms

            results['hands'][hand_name] = {
                'cards': combined_cards,
                'type': hand_type,
                'match': overall_match,
                'yolo_cards': yolo_cards,
                'yolo_sequences': yolo_sequences,
                'ocr_detections': ocr_detections,
                'processing_time_ms': hand_time
            }

            self.log_message(f"   YOLO Cards: {yolo_cards}")
            self.log_message(f"   YOLO Sequences: {yolo_sequences}")
            self.log_message(f"   OCR: {ocr_detections}")
            self.log_message(f"   Final: {combined_cards} ({hand_type}) - {hand_time:.1f}ms")

        # Winning seat detection with corrected model
        self.log_message("🏆 Detecting winning seat with capture_m.pt...")
        winning_info = self.detect_winning_seat_corrected()
        results['winning'] = winning_info

        results['total_processing_time'] = time.time() - start_time

        return results

    def detect_cards_and_sequences_yolo(self, hand_region):
        """Detect cards and sequences using YOLO model"""
        try:
            results = self.card_model(hand_region, verbose=False, conf=0.4)  # Lower confidence for better detection

            detected_cards = []
            detected_sequences = []
            detected_patterns = []

            if results[0].boxes:
                for box in results[0].boxes:
                    cls_id = int(box.cls[0])
                    label = self.card_model.names[cls_id]
                    confidence = float(box.conf[0])
                    xyxy = box.xyxy[0].cpu().numpy()

                    # Categorize detections
                    if label in self.card_values:
                        detected_cards.append({
                            'label': label,
                            'confidence': confidence,
                            'type': 'value',
                            'bbox': xyxy.tolist()
                        })
                    elif label in ['hearts', 'diamonds', 'clubs', 'spades']:
                        detected_cards.append({
                            'label': label,
                            'confidence': confidence,
                            'type': 'suit',
                            'bbox': xyxy.tolist()
                        })
                    elif label in ['sequence', 'straight']:
                        detected_sequences.append({
                            'label': label,
                            'confidence': confidence,
                            'type': 'sequence',
                            'bbox': xyxy.tolist()
                        })
                    elif label in ['pair', 'color', 'flush', 'high_card']:
                        detected_patterns.append({
                            'label': label,
                            'confidence': confidence,
                            'type': 'pattern',
                            'bbox': xyxy.tolist()
                        })

            # Extract card values
            card_values = [card['label'] for card in detected_cards if card['type'] == 'value']

            # Extract sequence indicators
            sequence_indicators = [seq['label'] for seq in detected_sequences]

            return {
                'cards': card_values,
                'sequences': sequence_indicators,
                'patterns': detected_patterns,
                'all_detections': detected_cards + detected_sequences + detected_patterns
            }

        except Exception as e:
            self.log_message(f"❌ YOLO detection error: {e}")
            return {'cards': [], 'sequences': [], 'patterns': [], 'all_detections': []}

    def extract_text_ocr(self, hand_region):
        """Extract text using OCR"""
        try:
            # Simple preprocessing
            gray = cv2.cvtColor(hand_region, cv2.COLOR_BGR2GRAY)

            # Resize if too small
            height, width = gray.shape
            if height < 50 or width < 50:
                scale = max(50/height, 50/width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                gray = cv2.resize(gray, (new_width, new_height))

            # OCR with card-specific settings
            results = self.easy_ocr.readtext(gray,
                                           allowlist='0123456789JQKA',
                                           width_ths=0.7,
                                           height_ths=0.7)

            # Extract card values
            ocr_cards = []
            for bbox, text, confidence in results:
                if confidence > 0.4:
                    # Clean and validate text
                    cleaned_text = self.clean_ocr_text(text)
                    if cleaned_text:
                        ocr_cards.append(cleaned_text)

            return ocr_cards

        except Exception as e:
            self.log_message(f"❌ OCR error: {e}")
            return []

    def clean_ocr_text(self, text):
        """Clean OCR text and extract card values"""
        if not text:
            return None

        text = text.upper().strip()

        # Direct matches
        valid_cards = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
        for card in valid_cards:
            if card in text:
                return card

        # Common OCR corrections
        corrections = {
            'O': 'Q', '0': 'Q',
            'I': 'J', 'l': 'J', '1': 'J',
            'S': '5', 'G': '6', 'T': '7', 'B': '8', 'Z': '2', 'E': '3'
        }

        for char in text:
            if char in corrections:
                return corrections[char]

        # Handle 10
        if any(pattern in text for pattern in ['10', 'IO', '1O', 'lo']):
            return '10'

        return None

    def combine_detections(self, yolo_cards, ocr_cards):
        """Combine YOLO and OCR detections"""
        all_cards = yolo_cards + ocr_cards

        # Count occurrences
        from collections import Counter
        card_counts = Counter(all_cards)

        # Take most frequent cards (up to 3)
        final_cards = [card for card, count in card_counts.most_common(3)]

        return final_cards

    def determine_hand_type_with_yolo(self, cards, yolo_sequences):
        """Determine hand type using YOLO sequence detection + card analysis"""

        # First priority: YOLO sequence detection
        if yolo_sequences:
            if any(seq in ['sequence', 'straight'] for seq in yolo_sequences):
                return "Sequence"

        # Second priority: Card-based analysis
        if len(cards) < 3:
            return "Insufficient cards"

        # Convert to numeric for sequence check
        numeric_values = []
        for card in cards:
            if card in self.card_values:
                numeric_values.append(self.card_values[card])

        if len(numeric_values) >= 3:
            numeric_values.sort()

            # Check for sequence
            if (len(numeric_values) >= 3 and
                numeric_values[1] == numeric_values[0] + 1 and
                numeric_values[2] == numeric_values[1] + 1):
                return "Sequence"

            # A-2-3 sequence
            if 14 in numeric_values and 2 in numeric_values and 3 in numeric_values:
                return "Sequence"

            # Check for pairs
            if len(set(numeric_values)) < len(numeric_values):
                return "Pair"

        return "High Card"

    def determine_hand_type(self, cards):
        """Legacy hand type determination (fallback)"""
        return self.determine_hand_type_with_yolo(cards, [])

    def detect_winning_seat_corrected(self):
        """Detect winning seat using capture_m.pt"""
        try:
            results = self.winning_seat_model(self.current_image, verbose=False, conf=0.4)

            winning_info = {
                'winning_seat': 'Unknown',
                'confidence': 0.0,
                'position': 'Unknown'
            }

            if results[0].boxes:
                # Get the highest confidence detection
                best_detection = None
                best_confidence = 0

                for box in results[0].boxes:
                    confidence = float(box.conf[0])
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_detection = box

                if best_detection is not None:
                    cls_id = int(best_detection.cls[0])
                    label = self.winning_seat_model.names[cls_id]
                    xyxy = best_detection.xyxy[0].cpu().numpy()
                    x_center = (xyxy[0] + xyxy[2]) / 2

                    # Determine which hand region this falls into
                    for hand_name, region in self.hand_regions.items():
                        if region:
                            x1, y1, x2, y2 = region
                            if x1 <= x_center <= x2:
                                winning_info = {
                                    'winning_seat': hand_name,
                                    'confidence': best_confidence,
                                    'position': hand_name.replace('_', ' ').title(),
                                    'detected_label': label
                                }
                                break

            self.log_message(f"🏆 Winning seat: {winning_info['winning_seat']} ({winning_info['confidence']:.3f})")
            return winning_info

        except Exception as e:
            self.log_message(f"❌ Winning seat detection error: {e}")
            return {'winning_seat': 'Unknown', 'confidence': 0.0, 'position': 'Unknown'}

    def analysis_completed(self, results):
        """Called when analysis is completed"""
        self.analysis_results = results
        self.status_label.config(text="Corrected analysis completed", fg='#00ff00')
        self.save_btn.config(state='normal')

        total_time = results['total_processing_time']
        self.log_message(f"✅ Corrected analysis completed in {total_time:.3f}s")

        # Update results table
        self.update_results_table(results)

        # Calculate accuracy
        total_matches = sum(1 for hand in results['hands'].values() if hand.get('match', False))
        accuracy = (total_matches / 3) * 100
        self.log_message(f"🎯 Accuracy: {accuracy:.1f}% ({total_matches}/3 hands correct)")

    def analysis_error(self, error):
        """Called when analysis fails"""
        self.status_label.config(text="Analysis failed", fg='#ff0000')
        self.log_message(f"❌ Analysis error: {error}")
        messagebox.showerror("Analysis Error", f"Analysis failed:\n{error}")

    def update_results_table(self, results):
        """Update results table"""
        # Clear table
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        analysis_type = results.get('analysis_type', 'manual_regions')

        for hand_name, hand_data in results['hands'].items():
            hand_display = hand_name.replace('_', ' ').title()
            cards_str = ', '.join(hand_data['cards']) if hand_data['cards'] else 'None'
            hand_type = hand_data['type']

            # Handle expected results
            if hand_name in EXPECTED_RESULTS:
                expected = EXPECTED_RESULTS[hand_name]
                expected_str = f"{', '.join(expected['cards'])} ({expected['type']})"
            else:
                expected_str = "Auto-detected region"

            match_str = "✅ YES" if hand_data['match'] else "❌ NO"

            # Show analysis mode
            if hand_data.get('auto_detected', False):
                mode_str = "🖼️ Auto"
            else:
                mode_str = "✋ Manual"

            time_str = f"{hand_data['processing_time_ms']:.1f}"

            self.results_tree.insert('', 'end', values=(
                hand_display, cards_str, hand_type, expected_str, match_str, mode_str, time_str
            ))

    def save_results(self):
        """Save results to database"""
        if not hasattr(self, 'analysis_results') or not self.analysis_results:
            messagebox.showerror("Error", "No analysis results to save!")
            return

        try:
            results = self.analysis_results
            hands = results['hands']
            winning = results.get('winning', {})
            models = results.get('models_used', {})

            # Calculate accuracy
            total_matches = sum(1 for hand in hands.values() if hand.get('match', False))
            accuracy = (total_matches / 3) * 100

            # Insert into database
            self.cursor.execute('''
                INSERT INTO corrected_fast_analysis
                (timestamp, screenshot_path,
                 left_hand_cards, left_hand_type, left_hand_match,
                 center_hand_cards, center_hand_type, center_hand_match,
                 right_hand_cards, right_hand_type, right_hand_match,
                 processing_time, accuracy_score, winning_seat, winning_confidence,
                 card_model, winning_model)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                results['timestamp'], results['screenshot_path'],

                ', '.join(hands.get('left_hand', {}).get('cards', [])),
                hands.get('left_hand', {}).get('type', 'Unknown'),
                hands.get('left_hand', {}).get('match', False),

                ', '.join(hands.get('center_hand', {}).get('cards', [])),
                hands.get('center_hand', {}).get('type', 'Unknown'),
                hands.get('center_hand', {}).get('match', False),

                ', '.join(hands.get('right_hand', {}).get('cards', [])),
                hands.get('right_hand', {}).get('type', 'Unknown'),
                hands.get('right_hand', {}).get('match', False),

                results.get('total_processing_time', 0),
                accuracy,
                winning.get('winning_seat', 'Unknown'),
                winning.get('confidence', 0),

                models.get('card_model', 'Unknown'),
                models.get('winning_model', 'Unknown')
            ))

            self.conn.commit()

            self.log_message("💾 Corrected analysis results saved!")
            messagebox.showinfo("Success", f"Results saved! Accuracy: {accuracy:.1f}%")

        except Exception as e:
            self.log_message(f"❌ Save error: {e}")
            messagebox.showerror("Save Error", f"Failed to save:\n{e}")

def main():
    root = tk.Tk()
    app = CorrectedFastAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
