"""
OCR Dependencies Installation Script
Installs and configures all OCR engines for the comparison tool
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False

def install_python_packages():
    """Install Python OCR packages"""
    packages = [
        "opencv-python",
        "numpy",
        "pillow",
        "ddddocr",
        "paddleocr",
        "easyocr", 
        "pytesseract",
        "transformers",
        "torch",
        "torchvision"
    ]
    
    print("📦 Installing Python packages...")
    for package in packages:
        success = run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
        if not success:
            print(f"⚠️ Failed to install {package}, continuing with others...")

def install_tesseract():
    """Install Tesseract OCR engine"""
    system = platform.system().lower()
    
    if system == "windows":
        print("🪟 Windows detected")
        print("📋 Please install Tesseract manually:")
        print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install the .exe file")
        print("3. Add Tesseract to your PATH environment variable")
        print("4. Typical installation path: C:\\Program Files\\Tesseract-OCR")
        
    elif system == "linux":
        print("🐧 Linux detected")
        # Try different package managers
        commands = [
            "sudo apt-get update && sudo apt-get install -y tesseract-ocr",
            "sudo yum install -y tesseract",
            "sudo dnf install -y tesseract",
            "sudo pacman -S tesseract"
        ]
        
        for cmd in commands:
            if run_command(cmd, "Installing Tesseract via package manager"):
                break
        else:
            print("⚠️ Could not install Tesseract automatically")
            print("Please install manually using your distribution's package manager")
    
    elif system == "darwin":  # macOS
        print("🍎 macOS detected")
        if run_command("brew install tesseract", "Installing Tesseract via Homebrew"):
            pass
        else:
            print("⚠️ Homebrew not found or failed")
            print("Please install Homebrew first: https://brew.sh/")
            print("Then run: brew install tesseract")

def test_installations():
    """Test if all OCR engines are working"""
    print("\n🧪 Testing OCR engine installations...")
    
    # Test ddddocr
    try:
        import ddddocr
        print("✅ ddddocr: Working")
    except ImportError:
        print("❌ ddddocr: Not available")
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR: Working")
    except ImportError:
        print("❌ PaddleOCR: Not available")
    
    # Test EasyOCR
    try:
        import easyocr
        print("✅ EasyOCR: Working")
    except ImportError:
        print("❌ EasyOCR: Not available")
    
    # Test Tesseract
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract: Working (version {version})")
    except Exception as e:
        print(f"❌ Tesseract: Not available ({e})")
    
    # Test OpenCV
    try:
        import cv2
        print(f"✅ OpenCV: Working (version {cv2.__version__})")
    except ImportError:
        print("❌ OpenCV: Not available")

def create_test_script():
    """Create a simple test script"""
    test_script = '''
import cv2
import numpy as np

# Test basic functionality
def test_basic_ocr():
    """Test basic OCR functionality"""
    print("🧪 Testing basic OCR functionality...")
    
    # Create a simple test image with text
    img = np.ones((100, 300, 3), dtype=np.uint8) * 255
    cv2.putText(img, "TEST 123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.imwrite("test_image.png", img)
    
    print("✅ Test image created: test_image.png")
    print("🔍 You can now test OCR engines with this image")

if __name__ == "__main__":
    test_basic_ocr()
'''
    
    with open("test_ocr_basic.py", "w") as f:
        f.write(test_script)
    
    print("📝 Created test_ocr_basic.py for basic testing")

def main():
    print("🎯 OCR Dependencies Installation")
    print("=" * 50)
    
    # Install Python packages
    install_python_packages()
    
    # Install Tesseract
    print("\n" + "=" * 50)
    install_tesseract()
    
    # Test installations
    print("\n" + "=" * 50)
    test_installations()
    
    # Create test script
    print("\n" + "=" * 50)
    create_test_script()
    
    print("\n🎉 Installation process completed!")
    print("\n📋 Next steps:")
    print("1. Run 'python quick_ocr_test.py' for command-line testing")
    print("2. Run 'python comprehensive_ocr_comparison.py' for GUI testing")
    print("3. Run 'python test_ocr_basic.py' to create a test image")
    
    print("\n💡 Tips:")
    print("- If Tesseract fails, install it manually from the official website")
    print("- Some OCR engines may take time to download models on first use")
    print("- For best results, use high-resolution, clear images")

if __name__ == "__main__":
    main()
