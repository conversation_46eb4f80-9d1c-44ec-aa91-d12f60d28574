# 🎯 Optimized YOLO Training for 80%+ Accuracy

This repository contains advanced YOLO training scripts designed to achieve minimum 80% accuracy for all classes, even with imbalanced datasets.

## 📊 Dataset Analysis Results

Your current dataset has significant challenges:
- **28 classes** with severe imbalance (153:1 ratio)
- **Critical underrepresented classes**: 
  - `sequence`: only 6 samples
  - `color`: only 39 samples
  - Numbers `2,3,4,5,6,7,8,9,10`: 45-75 samples each
- **Very small validation set**: only 4 images

## 🚀 Quick Start

### Option 1: Simple Training (Recommended)
```bash
python run_training.py
```

### Option 2: Advanced Hyperparameter Tuning
```bash
# First run basic training
python run_training.py

# Then run advanced tuning
python advanced_hyperparameter_tuning.py
```

## 🔧 What the Scripts Do

### 1. `optimized_yolo_training.py`
**Main training script with advanced techniques:**

- **Data Augmentation**: Heavy augmentation for underrepresented classes
- **Class Balancing**: Automatically augments classes with <80 samples
- **Validation Split**: Creates balanced validation set (15% of data)
- **Advanced Hyperparameters**: Optimized for high accuracy
- **Class Weights**: Calculated to handle imbalance

**Key Features:**
- Uses YOLOv11n for efficiency
- AdamW optimizer with cosine learning rate
- Advanced augmentation pipeline (Albumentations)
- Automatic class balancing
- Early stopping with patience

### 2. `advanced_hyperparameter_tuning.py`
**Hyperparameter optimization using Optuna:**

- **Automated Tuning**: Tests 30+ hyperparameter combinations
- **Objective Optimization**: Maximizes mean Average Precision (mAP)
- **Final Training**: Uses best parameters for 500 epochs
- **Performance Analysis**: Detailed per-class accuracy report

### 3. `run_training.py`
**Simple runner script:**

- Installs dependencies automatically
- Checks GPU availability
- Runs optimized training pipeline

## 📈 Expected Improvements

### Before Optimization:
- Severe class imbalance (153:1 ratio)
- Tiny validation set (4 images)
- No augmentation for rare classes
- Default hyperparameters

### After Optimization:
- **Balanced classes**: All classes have 80+ samples
- **Proper validation**: 15% balanced split
- **Heavy augmentation**: For underrepresented classes
- **Optimized hyperparameters**: Tuned for your specific dataset
- **Advanced techniques**: Focal loss, cosine LR, class weights

## 🎯 Techniques Used for 80%+ Accuracy

### 1. **Data Augmentation Strategy**
```python
# Heavy augmentation for rare classes
- HorizontalFlip, RandomBrightnessContrast
- HueSaturationValue, RandomGamma
- GaussNoise, MotionBlur
- RandomRotate90, ShiftScaleRotate
```

### 2. **Class Balancing**
- Automatically identifies classes with <80 samples
- Generates augmented samples to reach target count
- Preserves original label information

### 3. **Advanced Training Configuration**
- **Model**: YOLOv11n → YOLOv11s (for final model)
- **Optimizer**: AdamW with cosine learning rate
- **Loss**: Optimized box, class, and DFL losses
- **Augmentation**: Mosaic, HSV, geometric transforms
- **Regularization**: Weight decay, label smoothing

### 4. **Hyperparameter Optimization**
- Learning rate: 0.001-0.1 (log scale)
- Momentum: 0.8-0.99
- Weight decay: 0.0001-0.001
- Box/Class loss weights: Optimized
- Augmentation parameters: Fine-tuned

## 📁 Output Structure

```
runs/
├── train/
│   ├── optimized_model/          # Initial training results
│   │   ├── weights/
│   │   │   ├── best.pt          # Best model weights
│   │   │   └── last.pt          # Last epoch weights
│   │   ├── results.png          # Training curves
│   │   └── confusion_matrix.png # Confusion matrix
│   └── final_optimized_model/    # Final tuned model
└── tune/                         # Hyperparameter tuning trials
```

## 🔍 Monitoring Training

### Key Metrics to Watch:
- **mAP@0.5**: Should reach >0.8 for all classes
- **Precision/Recall**: Balanced across classes
- **Loss curves**: Should converge smoothly
- **Validation mAP**: Should not overfit

### Training Progress:
1. **Epochs 1-50**: Initial learning and convergence
2. **Epochs 50-150**: Fine-tuning and stabilization
3. **Epochs 150-300**: Final optimization
4. **Early stopping**: If no improvement for 50 epochs

## ⚠️ Troubleshooting

### Common Issues:

1. **GPU Memory Error**:
   ```python
   # Reduce batch size in training script
   batch=8  # Instead of 16
   ```

2. **Low Accuracy on Specific Classes**:
   - Check class distribution after augmentation
   - Increase target_min_samples for those classes
   - Add more class-specific augmentation

3. **Overfitting**:
   - Increase validation split ratio
   - Add more regularization
   - Reduce model complexity

4. **Slow Training**:
   - Use smaller image size (imgsz=416)
   - Reduce workers if CPU limited
   - Use mixed precision (amp=True)

## 🎯 Expected Results

With these optimizations, you should achieve:
- **Overall mAP@0.5**: >85%
- **Per-class accuracy**: >80% for all 28 classes
- **Training time**: 4-8 hours (depending on GPU)
- **Model size**: ~6-14MB (efficient for deployment)

## 📊 Performance Validation

After training, the script will show:
```
📈 Detailed Per-Class Performance:
------------------------------------------------------------
Class           AP@0.5     AP@0.5:0.95  Status    
------------------------------------------------------------
10              0.856      0.542        ✅ Good
2               0.823      0.518        ✅ Good
sequence        0.789      0.456        ⚠️ Weak
...
```

## 🚀 Next Steps

1. **Run basic training**: `python run_training.py`
2. **Analyze results**: Check per-class performance
3. **Fine-tune if needed**: Run advanced hyperparameter tuning
4. **Deploy model**: Use best.pt for inference
5. **Monitor performance**: Test on real data

## 💡 Tips for Maximum Accuracy

1. **Data Quality**: Ensure labels are accurate
2. **Class Balance**: Monitor augmentation results
3. **Validation**: Use stratified sampling
4. **Hyperparameters**: Let Optuna find optimal values
5. **Ensemble**: Combine multiple models if needed

Good luck with your training! 🎉
