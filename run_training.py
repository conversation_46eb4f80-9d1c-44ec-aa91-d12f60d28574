"""
Quick Start Training Script
Run this file to automatically install dependencies and start optimized YOLO training
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False
    return True

def check_gpu():
    """Check if GPU is available"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🚀 GPU detected: {gpu_name}")
            print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
            return True
        else:
            print("⚠️ No GPU detected. Training will use CPU (slower)")
            return False
    except ImportError:
        print("⚠️ PyTorch not installed. Installing dependencies first...")
        return False

def main():
    print("🎯 YOLO Optimized Training Pipeline")
    print("=" * 50)
    
    # Check if data.yaml exists
    if not os.path.exists("First.v2i.yolov8/data.yaml"):
        print("❌ Error: First.v2i.yolov8/data.yaml not found!")
        print("Please ensure your dataset is in the correct location.")
        return
    
    # Install dependencies
    if not install_requirements():
        return
    
    # Check GPU
    check_gpu()
    
    # Import and run training
    try:
        print("\n🚀 Starting optimized training...")
        from optimized_yolo_training import train_optimized_yolo
        
        model, results = train_optimized_yolo(
            data_yaml_path="First.v2i.yolov8/data.yaml",
            epochs=300,
            patience=50,
            target_accuracy=0.8
        )
        
        print("\n🎉 Training completed successfully!")
        print("📁 Check 'runs/train/optimized_model' for results")
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("Please check the error message above and try again.")

if __name__ == "__main__":
    main()
