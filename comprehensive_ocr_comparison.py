"""
Comprehensive OCR Comparison Tool
Tests multiple OCR engines on selected image regions with grayscale conversion
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import os
import threading
import time
from collections import defaultdict

# OCR Engine imports
try:
    import ddddocr
    DDDDOCR_AVAILABLE = True
except ImportError:
    DDDDOCR_AVAILABLE = False

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

try:
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    TROCR_AVAILABLE = True
except ImportError:
    TROCR_AVAILABLE = False

class OCRComparison:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Comprehensive OCR Comparison Tool")
        self.root.geometry("1400x900")
        
        # Variables
        self.image_path = None
        self.original_image = None
        self.display_image = None
        self.canvas = None
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        self.selected_regions = []
        
        # OCR Engines
        self.ocr_engines = {}
        self.init_ocr_engines()
        
        # Setup GUI
        self.setup_gui()
        
    def init_ocr_engines(self):
        """Initialize available OCR engines"""
        print("🔧 Initializing OCR engines...")
        
        if DDDDOCR_AVAILABLE:
            try:
                self.ocr_engines['ddddocr'] = ddddocr.DdddOcr(show_ad=False)
                print("✅ ddddocr initialized")
            except Exception as e:
                print(f"❌ ddddocr failed: {e}")
        
        if PADDLEOCR_AVAILABLE:
            try:
                self.ocr_engines['paddleocr'] = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                print("✅ PaddleOCR initialized")
            except Exception as e:
                print(f"❌ PaddleOCR failed: {e}")
        
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['en'], gpu=False)
                print("✅ EasyOCR initialized")
            except Exception as e:
                print(f"❌ EasyOCR failed: {e}")
        
        if TESSERACT_AVAILABLE:
            try:
                # Test tesseract
                pytesseract.get_tesseract_version()
                self.ocr_engines['tesseract'] = 'tesseract'
                print("✅ Tesseract initialized")
            except Exception as e:
                print(f"❌ Tesseract failed: {e}")
        
        if TROCR_AVAILABLE:
            try:
                self.ocr_engines['trocr_processor'] = TrOCRProcessor.from_pretrained('microsoft/trocr-base-printed')
                self.ocr_engines['trocr_model'] = VisionEncoderDecoderModel.from_pretrained('microsoft/trocr-base-printed')
                print("✅ TrOCR initialized")
            except Exception as e:
                print(f"❌ TrOCR failed: {e}")
        
        print(f"📊 Total OCR engines available: {len([k for k in self.ocr_engines.keys() if not k.endswith('_model') and not k.endswith('_processor')])}")
    
    def setup_gui(self):
        """Setup the GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File selection
        ttk.Button(control_frame, text="📁 Select Image", command=self.select_image).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🔄 Clear Selections", command=self.clear_selections).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="🔍 Run OCR Comparison", command=self.run_ocr_comparison).pack(side=tk.LEFT, padx=(0, 10))
        
        # Options
        self.grayscale_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="Convert to Grayscale", variable=self.grayscale_var).pack(side=tk.LEFT, padx=(10, 0))
        
        # Content frame
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Image
        left_frame = ttk.LabelFrame(content_frame, text="Image & Region Selection")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Canvas for image display
        self.canvas = tk.Canvas(left_frame, bg='white', cursor='crosshair')
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Bind mouse events for region selection
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)
        
        # Right panel - Results
        right_frame = ttk.LabelFrame(content_frame, text="OCR Results")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Results text area with scrollbar
        results_frame = ttk.Frame(right_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select an image to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
        # Instructions
        instructions = """
Instructions:
1. Click 'Select Image' to load an image
2. Click and drag on the image to select regions for OCR
3. Multiple regions can be selected
4. Click 'Run OCR Comparison' to test all available OCR engines
5. Results will show accuracy scores and best matches
        """
        self.results_text.insert(tk.END, instructions)
    
    def select_image(self):
        """Select and load an image"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff *.gif"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path):
        """Load and display the selected image"""
        try:
            self.image_path = file_path
            self.original_image = cv2.imread(file_path)
            
            if self.original_image is None:
                messagebox.showerror("Error", "Could not load the image file")
                return
            
            # Convert BGR to RGB for display
            rgb_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            
            # Resize image to fit canvas while maintaining aspect ratio
            canvas_width = 800
            canvas_height = 600
            
            h, w = rgb_image.shape[:2]
            scale = min(canvas_width/w, canvas_height/h)
            
            new_w = int(w * scale)
            new_h = int(h * scale)
            
            resized_image = cv2.resize(rgb_image, (new_w, new_h))
            
            # Convert to PIL Image and then to PhotoImage
            pil_image = Image.fromarray(resized_image)
            self.display_image = ImageTk.PhotoImage(pil_image)
            
            # Update canvas
            self.canvas.delete("all")
            self.canvas.config(width=new_w, height=new_h)
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
            
            # Store scale factor for coordinate conversion
            self.scale_factor = scale
            
            self.status_var.set(f"Image loaded: {os.path.basename(file_path)} ({w}x{h})")
            self.clear_selections()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
    
    def start_selection(self, event):
        """Start region selection"""
        if self.original_image is None:
            return
        
        self.selection_start = (event.x, event.y)
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.selection_start is None:
            return
        
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
        
        self.selection_rect = self.canvas.create_rectangle(
            self.selection_start[0], self.selection_start[1],
            event.x, event.y,
            outline='red', width=2
        )
    
    def end_selection(self, event):
        """End region selection and store coordinates"""
        if self.selection_start is None:
            return
        
        self.selection_end = (event.x, event.y)
        
        # Convert display coordinates to original image coordinates
        x1 = int(min(self.selection_start[0], self.selection_end[0]) / self.scale_factor)
        y1 = int(min(self.selection_start[1], self.selection_end[1]) / self.scale_factor)
        x2 = int(max(self.selection_start[0], self.selection_end[0]) / self.scale_factor)
        y2 = int(max(self.selection_start[1], self.selection_end[1]) / self.scale_factor)
        
        # Ensure coordinates are within image bounds
        h, w = self.original_image.shape[:2]
        x1 = max(0, min(x1, w))
        y1 = max(0, min(y1, h))
        x2 = max(0, min(x2, w))
        y2 = max(0, min(y2, h))
        
        if x2 - x1 > 10 and y2 - y1 > 10:  # Minimum size check
            self.selected_regions.append((x1, y1, x2, y2))
            self.status_var.set(f"Region {len(self.selected_regions)} selected: ({x1},{y1}) to ({x2},{y2})")
        
        self.selection_start = None
        self.selection_end = None
    
    def clear_selections(self):
        """Clear all selected regions"""
        self.selected_regions = []
        if self.canvas:
            self.canvas.delete("all")
            if self.display_image:
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.display_image)
        self.status_var.set("Selections cleared")
    
    def preprocess_image(self, image, grayscale=True):
        """Preprocess image for better OCR results"""
        processed = image.copy()
        
        if grayscale:
            processed = cv2.cvtColor(processed, cv2.COLOR_BGR2GRAY)
            
            # Apply additional preprocessing
            # Gaussian blur to reduce noise
            processed = cv2.GaussianBlur(processed, (1, 1), 0)
            
            # Adaptive thresholding
            processed = cv2.adaptiveThreshold(
                processed, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
        
        return processed
    
    def run_ocr_with_engine(self, engine_name, image):
        """Run OCR with a specific engine"""
        try:
            if engine_name == 'ddddocr':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    _, buffer = cv2.imencode('.png', cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                else:
                    _, buffer = cv2.imencode('.png', image)
                result = self.ocr_engines['ddddocr'].classification(buffer.tobytes())
                return result.strip()
            
            elif engine_name == 'paddleocr':
                result = self.ocr_engines['paddleocr'].ocr(image, cls=True)
                if result and result[0]:
                    text_parts = []
                    for line in result[0]:
                        if len(line) > 1:
                            text_parts.append(line[1][0])
                    return ' '.join(text_parts).strip()
                return ""
            
            elif engine_name == 'easyocr':
                result = self.ocr_engines['easyocr'].readtext(image, detail=0)
                return ' '.join(result).strip()
            
            elif engine_name == 'tesseract':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(image)
                else:
                    pil_image = Image.fromarray(image)
                result = pytesseract.image_to_string(pil_image, config='--psm 8')
                return result.strip()
            
            elif engine_name == 'trocr':
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(image)
                else:
                    pil_image = Image.fromarray(image).convert('RGB')
                
                pixel_values = self.ocr_engines['trocr_processor'](pil_image, return_tensors="pt").pixel_values
                generated_ids = self.ocr_engines['trocr_model'].generate(pixel_values)
                result = self.ocr_engines['trocr_processor'].batch_decode(generated_ids, skip_special_tokens=True)[0]
                return result.strip()
            
        except Exception as e:
            return f"Error: {str(e)}"
        
        return ""
    
    def calculate_similarity(self, text1, text2):
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0
        
        # Simple character-based similarity
        text1 = text1.lower().strip()
        text2 = text2.lower().strip()
        
        if text1 == text2:
            return 1.0
        
        # Levenshtein distance based similarity
        def levenshtein_distance(s1, s2):
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)
            
            if len(s2) == 0:
                return len(s1)
            
            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row
            
            return previous_row[-1]
        
        max_len = max(len(text1), len(text2))
        distance = levenshtein_distance(text1, text2)
        similarity = 1 - (distance / max_len)
        return max(0.0, similarity)
    
    def run_ocr_comparison(self):
        """Run OCR comparison on all selected regions"""
        if not self.selected_regions:
            messagebox.showwarning("Warning", "Please select at least one region first")
            return
        
        if not self.ocr_engines:
            messagebox.showerror("Error", "No OCR engines available")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "🔍 Running OCR Comparison...\n")
        self.results_text.insert(tk.END, "=" * 60 + "\n\n")
        
        # Run OCR in a separate thread to prevent GUI freezing
        threading.Thread(target=self._run_ocr_thread, daemon=True).start()
    
    def _run_ocr_thread(self):
        """Thread function for running OCR"""
        try:
            all_results = []
            
            for i, (x1, y1, x2, y2) in enumerate(self.selected_regions):
                self.root.after(0, lambda i=i: self.status_var.set(f"Processing region {i+1}/{len(self.selected_regions)}..."))
                
                # Extract region
                region = self.original_image[y1:y2, x1:x2]
                
                # Test both original and grayscale
                results = {}
                
                # Original image
                for engine_name in self.ocr_engines:
                    if engine_name.endswith('_model') or engine_name.endswith('_processor'):
                        continue
                    
                    start_time = time.time()
                    result = self.run_ocr_with_engine(engine_name, region)
                    end_time = time.time()
                    
                    results[f"{engine_name}_original"] = {
                        'text': result,
                        'time': end_time - start_time
                    }
                
                # Grayscale image (if enabled)
                if self.grayscale_var.get():
                    gray_region = self.preprocess_image(region, grayscale=True)
                    
                    for engine_name in self.ocr_engines:
                        if engine_name.endswith('_model') or engine_name.endswith('_processor'):
                            continue
                        
                        start_time = time.time()
                        result = self.run_ocr_with_engine(engine_name, gray_region)
                        end_time = time.time()
                        
                        results[f"{engine_name}_grayscale"] = {
                            'text': result,
                            'time': end_time - start_time
                        }
                
                all_results.append({
                    'region': (x1, y1, x2, y2),
                    'results': results
                })
            
            # Display results
            self.root.after(0, lambda: self._display_results(all_results))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"OCR processing failed: {str(e)}"))
    
    def _display_results(self, all_results):
        """Display OCR comparison results"""
        self.results_text.delete(1.0, tk.END)
        
        for i, region_data in enumerate(all_results):
            x1, y1, x2, y2 = region_data['region']
            results = region_data['results']
            
            self.results_text.insert(tk.END, f"📍 Region {i+1}: ({x1},{y1}) to ({x2},{y2})\n")
            self.results_text.insert(tk.END, "-" * 50 + "\n")
            
            # Group results by engine
            engine_results = defaultdict(dict)
            for key, data in results.items():
                if '_original' in key:
                    engine = key.replace('_original', '')
                    engine_results[engine]['original'] = data
                elif '_grayscale' in key:
                    engine = key.replace('_grayscale', '')
                    engine_results[engine]['grayscale'] = data
            
            # Display results for each engine
            for engine, data in engine_results.items():
                self.results_text.insert(tk.END, f"\n🔧 {engine.upper()}:\n")
                
                if 'original' in data:
                    text = data['original']['text']
                    time_taken = data['original']['time']
                    self.results_text.insert(tk.END, f"  Original: '{text}' ({time_taken:.3f}s)\n")
                
                if 'grayscale' in data:
                    text = data['grayscale']['text']
                    time_taken = data['grayscale']['time']
                    self.results_text.insert(tk.END, f"  Grayscale: '{text}' ({time_taken:.3f}s)\n")
            
            # Find best results (most common text)
            all_texts = [data['text'] for data in results.values() if data['text']]
            if all_texts:
                from collections import Counter
                text_counts = Counter(all_texts)
                best_text = text_counts.most_common(1)[0][0]
                confidence = text_counts.most_common(1)[0][1] / len(all_texts)
                
                self.results_text.insert(tk.END, f"\n🏆 Best Result: '{best_text}' (Confidence: {confidence:.1%})\n")
            
            self.results_text.insert(tk.END, "\n" + "=" * 60 + "\n\n")
        
        self.status_var.set("OCR comparison completed")
    
    def run(self):
        """Start the application"""
        # Load default image if provided
        default_image = r"D:\Machine Learning Journey\Vscode Projects\Eziline Projects\Client\testing\Screenshot 2025-06-05 160750.png"
        if os.path.exists(default_image):
            self.load_image(default_image)
        
        self.root.mainloop()

if __name__ == "__main__":
    app = OCRComparison()
    app.run()
