"""
Dependency Fixer for OCR Tools
Fixes common PyTorch and OCR dependency issues
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False

def fix_pytorch_issues():
    """Fix common PyTorch issues"""
    print("🔧 Fixing PyTorch issues...")
    
    # Uninstall problematic packages
    packages_to_remove = ['torch', 'torchvision', 'torchaudio', 'easyocr']
    for package in packages_to_remove:
        run_command(f"{sys.executable} -m pip uninstall {package} -y", f"Removing {package}")
    
    # Clear pip cache
    run_command(f"{sys.executable} -m pip cache purge", "Clearing pip cache")
    
    # Install CPU-only PyTorch (more stable)
    print("📦 Installing CPU-only PyTorch...")
    pytorch_install = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    run_command(pytorch_install, "Installing PyTorch CPU")
    
    # Try to install EasyOCR again
    run_command(f"{sys.executable} -m pip install easyocr", "Installing EasyOCR")

def install_minimal_ocr():
    """Install minimal OCR engines that work reliably"""
    print("📦 Installing minimal OCR engines...")
    
    # Core dependencies
    core_packages = [
        "opencv-python",
        "numpy", 
        "pillow",
        "ddddocr",
        "pytesseract"
    ]
    
    for package in core_packages:
        run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}")
    
    # Try PaddleOCR (usually more stable than EasyOCR)
    run_command(f"{sys.executable} -m pip install paddleocr", "Installing PaddleOCR")

def test_ocr_engines():
    """Test which OCR engines are working"""
    print("\n🧪 Testing OCR engines...")
    
    working_engines = []
    
    # Test ddddocr
    try:
        import ddddocr
        working_engines.append("ddddocr")
        print("✅ ddddocr: Working")
    except Exception as e:
        print(f"❌ ddddocr: {e}")
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        working_engines.append("paddleocr")
        print("✅ PaddleOCR: Working")
    except Exception as e:
        print(f"❌ PaddleOCR: {e}")
    
    # Test Tesseract
    try:
        import pytesseract
        pytesseract.get_tesseract_version()
        working_engines.append("tesseract")
        print("✅ Tesseract: Working")
    except Exception as e:
        print(f"❌ Tesseract: {e}")
    
    # Test EasyOCR
    try:
        import easyocr
        working_engines.append("easyocr")
        print("✅ EasyOCR: Working")
    except Exception as e:
        print(f"❌ EasyOCR: {e}")
    
    print(f"\n📊 Working engines: {working_engines}")
    return working_engines

def create_simple_test():
    """Create a simple test script"""
    test_code = '''
import cv2
import numpy as np

def create_test_image():
    """Create a simple test image with text"""
    # Create white background
    img = np.ones((100, 300, 3), dtype=np.uint8) * 255
    
    # Add text
    cv2.putText(img, "A", (50, 60), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    cv2.putText(img, "K", (150, 60), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    cv2.putText(img, "Q", (250, 60), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    
    # Save image
    cv2.imwrite("test_cards.png", img)
    print("✅ Test image created: test_cards.png")
    return img

def test_available_ocr():
    """Test available OCR engines"""
    img = create_test_image()
    
    # Test ddddocr
    try:
        import ddddocr
        ocr = ddddocr.DdddOcr(show_ad=False)
        
        # Test each letter
        for i, letter in enumerate(['A', 'K', 'Q']):
            x = 30 + i * 100
            region = img[20:80, x:x+60]
            cv2.imwrite(f"test_region_{letter}.png", region)
            
            _, buffer = cv2.imencode('.png', region)
            result = ocr.classification(buffer.tobytes())
            print(f"ddddocr - {letter}: '{result}'")
    
    except Exception as e:
        print(f"ddddocr test failed: {e}")
    
    # Test PaddleOCR
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        
        result = ocr.ocr(img, cls=True)
        if result and result[0]:
            texts = [line[1][0] for line in result[0] if len(line) > 1]
            print(f"PaddleOCR: {texts}")
    
    except Exception as e:
        print(f"PaddleOCR test failed: {e}")
    
    # Test Tesseract
    try:
        import pytesseract
        from PIL import Image
        
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        result = pytesseract.image_to_string(pil_img, config='--psm 8')
        print(f"Tesseract: '{result.strip()}'")
    
    except Exception as e:
        print(f"Tesseract test failed: {e}")

if __name__ == "__main__":
    create_test_image()
    test_available_ocr()
'''
    
    with open("test_ocr_simple.py", "w") as f:
        f.write(test_code)
    
    print("📝 Created test_ocr_simple.py")

def main():
    print("🔧 OCR Dependency Fixer")
    print("=" * 50)
    
    print("Choose an option:")
    print("1. Fix PyTorch issues (recommended if EasyOCR fails)")
    print("2. Install minimal OCR engines only")
    print("3. Test current OCR engines")
    print("4. Create simple test")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == "1":
        fix_pytorch_issues()
        test_ocr_engines()
    elif choice == "2":
        install_minimal_ocr()
        test_ocr_engines()
    elif choice == "3":
        working_engines = test_ocr_engines()
        if working_engines:
            print(f"\n✅ You can use: {', '.join(working_engines)}")
        else:
            print("\n❌ No working OCR engines found")
    elif choice == "4":
        create_simple_test()
    else:
        print("Invalid choice")
        return
    
    print("\n🎉 Process completed!")
    print("\n📋 Next steps:")
    print("1. Run 'python simple_ocr_comparison.py' for GUI testing")
    print("2. Run 'python test_ocr_simple.py' to test OCR engines")

if __name__ == "__main__":
    main()
