train: /kaggle/input/poker-clubs-suits/First.v2i.yolov8/train/images
val: /kaggle/input/poker-clubs-suits/First.v2i.yolov8/valid/images
test: /kaggle/input/poker-clubs-suits/First.v2i.yolov8/test/images

nc: 28
names: ['10', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'J', 'K', 'Q', 'blue_seat', 'clubs', 'color', 'diamonds', 'golden_seat', 'hearts', 'high_card', 'losing_seat', 'pair', 'purple_seat', 'sequence', 'spades', 'winning_seat']

roboflow:
  workspace: object-qyubn
  project: my-first-pf4uv
  version: 2
  license: CC BY 4.0
  url: https://universe.roboflow.com/object-qyubn/my-first-pf4uv/dataset/2
