"""
Kaggle-Optimized YOLO Training Script for 80%+ Accuracy
Designed specifically for Kaggle environment with GPU acceleration
"""

import os
import yaml
import torch
import numpy as np
from ultralytics import YOLO
from collections import Counter
import glob
import shutil
from pathlib import Path
import random
import cv2
from sklearn.model_selection import train_test_split
import albumentations as A

class KaggleDatasetBalancer:
    def __init__(self, data_yaml_path):
        self.data_yaml_path = data_yaml_path
        with open(data_yaml_path, 'r') as f:
            self.data_config = yaml.safe_load(f)
        self.class_names = self.data_config['names']
        self.nc = self.data_config['nc']
        
    def analyze_class_distribution(self, labels_dir):
        """Analyze class distribution in dataset"""
        class_counts = Counter()
        label_files = glob.glob(os.path.join(labels_dir, '*.txt'))
        
        for label_file in label_files:
            with open(label_file, 'r') as f:
                for line in f:
                    if line.strip():
                        class_id = int(line.strip().split()[0])
                        class_counts[class_id] += 1
        return class_counts
    
    def augment_underrepresented_classes(self, train_images_dir, train_labels_dir, target_min_samples=100):
        """Augment underrepresented classes using advanced augmentation"""
        class_counts = self.analyze_class_distribution(train_labels_dir)
        
        # Kaggle-optimized augmentation pipeline
        heavy_augment = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
            A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.8),
            A.RandomGamma(gamma_limit=(80, 120), p=0.5),
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
            A.MotionBlur(blur_limit=3, p=0.3),
            A.RandomRotate90(p=0.3),
            A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=15, p=0.5),
        ])
        
        # Find underrepresented classes
        max_count = max(class_counts.values()) if class_counts else 0
        underrepresented = {class_id: count for class_id, count in class_counts.items() 
                          if count < target_min_samples}
        
        print(f"🔄 Augmenting {len(underrepresented)} underrepresented classes...")
        
        for class_id, current_count in underrepresented.items():
            needed_samples = target_min_samples - current_count
            class_name = self.class_names[class_id]
            print(f"Class {class_id} ({class_name}): {current_count} -> {target_min_samples} (+{needed_samples})")
            
            # Find images containing this class
            class_images = []
            for label_file in glob.glob(os.path.join(train_labels_dir, '*.txt')):
                with open(label_file, 'r') as f:
                    if any(int(line.strip().split()[0]) == class_id for line in f if line.strip()):
                        image_name = os.path.basename(label_file).replace('.txt', '')
                        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                            img_path = os.path.join(train_images_dir, image_name + ext)
                            if os.path.exists(img_path):
                                class_images.append((img_path, label_file))
                                break
            
            # Generate augmented samples
            for i in range(needed_samples):
                if not class_images:
                    continue
                    
                img_path, label_path = random.choice(class_images)
                
                # Load and augment image
                image = cv2.imread(img_path)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                
                augmented = heavy_augment(image=image)
                augmented_image = cv2.cvtColor(augmented['image'], cv2.COLOR_RGB2BGR)
                
                # Save augmented image and copy label
                base_name = os.path.splitext(os.path.basename(img_path))[0]
                aug_img_name = f"{base_name}_aug_{i}.jpg"
                aug_label_name = f"{base_name}_aug_{i}.txt"
                
                cv2.imwrite(os.path.join(train_images_dir, aug_img_name), augmented_image)
                shutil.copy2(label_path, os.path.join(train_labels_dir, aug_label_name))

def calculate_class_weights(labels_dir, nc):
    """Calculate class weights for focal loss"""
    class_counts = Counter()
    total_samples = 0
    
    for label_file in glob.glob(os.path.join(labels_dir, '*.txt')):
        with open(label_file, 'r') as f:
            for line in f:
                if line.strip():
                    class_id = int(line.strip().split()[0])
                    class_counts[class_id] += 1
                    total_samples += 1
    
    # Calculate inverse frequency weights
    weights = []
    for i in range(nc):
        count = class_counts.get(i, 1)  # Avoid division by zero
        weight = total_samples / (nc * count)
        weights.append(weight)
    
    return weights

def train_kaggle_optimized_yolo(data_yaml_path, epochs=200, patience=30, target_accuracy=0.8):
    """Train YOLO optimized for Kaggle environment"""
    
    print("🚀 Starting Kaggle-Optimized YOLO Training")
    print("=" * 50)
    
    # Check GPU availability
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🔧 Using device: {device}")
    if torch.cuda.is_available():
        print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Initialize dataset balancer
    balancer = KaggleDatasetBalancer(data_yaml_path)
    
    # Get paths from data.yaml
    with open(data_yaml_path, 'r') as f:
        data_config = yaml.safe_load(f)
    
    train_images_dir = data_config['train'].replace('/images', '/images')
    train_labels_dir = data_config['train'].replace('/images', '/labels')
    
    # Step 1: Augment underrepresented classes
    print("📈 Augmenting underrepresented classes...")
    balancer.augment_underrepresented_classes(train_images_dir, train_labels_dir, target_min_samples=80)
    
    # Step 2: Calculate class weights
    print("⚖️ Calculating class weights...")
    class_weights = calculate_class_weights(train_labels_dir, data_config['nc'])
    
    # Step 3: Initialize model
    print("🤖 Initializing YOLO model...")
    model = YOLO('yolo11n.pt')  # YOLOv11 nano for efficiency
    
    # Step 4: Kaggle-optimized training parameters
    print("🎯 Starting training with Kaggle-optimized hyperparameters...")
    
    # Determine batch size based on GPU memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        if gpu_memory >= 15:
            batch_size = 32
        elif gpu_memory >= 8:
            batch_size = 24
        else:
            batch_size = 16
    else:
        batch_size = 8
    
    print(f"📦 Using batch size: {batch_size}")
    
    results = model.train(
        data=data_yaml_path,
        epochs=epochs,
        patience=patience,
        batch=batch_size,
        imgsz=640,
        save=True,
        save_period=20,
        cache=True,
        device=device,
        workers=2,  # Reduced for Kaggle stability
        project='/kaggle/working/runs/train',
        name='kaggle_optimized_model',
        exist_ok=True,
        pretrained=True,
        optimizer='AdamW',
        verbose=True,
        seed=42,
        deterministic=True,
        single_cls=False,
        rect=False,
        cos_lr=True,
        close_mosaic=10,
        resume=False,
        amp=True,
        fraction=1.0,
        profile=False,
        freeze=None,
        # Kaggle-optimized hyperparameters
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        box=7.5,
        cls=0.5,
        dfl=1.5,
        pose=12.0,
        kobj=2.0,
        label_smoothing=0.0,
        nbs=64,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.0,
        translate=0.1,
        scale=0.5,
        shear=0.0,
        perspective=0.0,
        flipud=0.0,
        fliplr=0.5,
        mosaic=1.0,
        mixup=0.0,
        copy_paste=0.0
    )
    
    print("✅ Training completed!")
    print(f"📊 Best model saved at: {model.trainer.best}")
    
    # Step 5: Validate and analyze results
    print("🔍 Validating model performance...")
    metrics = model.val()
    
    # Print per-class results
    print("\n📈 Per-class Performance:")
    print("-" * 60)
    print(f"{'Class':<15} {'AP@0.5':<10} {'AP@0.5:0.95':<12} {'Status':<10}")
    print("-" * 60)
    
    weak_classes = []
    
    if hasattr(metrics, 'ap_class_index') and hasattr(metrics, 'ap'):
        for i, class_idx in enumerate(metrics.ap_class_index):
            if class_idx < len(data_config['names']):
                class_name = data_config['names'][class_idx]
                ap50 = metrics.ap50[i] if hasattr(metrics, 'ap50') else 0
                ap = metrics.ap[i] if hasattr(metrics, 'ap') else 0
                
                status = "✅ Good" if ap50 > 0.8 else "⚠️ Weak" if ap50 > 0.6 else "❌ Poor"
                if ap50 < 0.8:
                    weak_classes.append((class_name, ap50))
                
                print(f"{class_name:<15} {ap50:<10.3f} {ap:<12.3f} {status:<10}")
    
    if weak_classes:
        print(f"\n⚠️ Classes below 80% accuracy:")
        for class_name, ap50 in weak_classes:
            print(f"  - {class_name}: {ap50:.1%}")
    else:
        print("\n🎉 All classes achieved 80%+ accuracy!")
    
    return model, results

def main():
    """Main function for Kaggle execution"""
    # Set random seeds for reproducibility
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # Kaggle dataset path
    data_yaml_path = "/kaggle/input/poker-clubs-suits/First.v2i.yolov8/data.yaml"
    
    # Check if dataset exists
    if not os.path.exists(data_yaml_path):
        print("❌ Error: Dataset not found!")
        print("Please ensure the dataset is properly uploaded to Kaggle.")
        return
    
    print("🎯 Kaggle YOLO Training Pipeline")
    print("=" * 50)
    
    # Start training
    model, results = train_kaggle_optimized_yolo(
        data_yaml_path=data_yaml_path,
        epochs=200,  # Reduced for Kaggle time limits
        patience=30,
        target_accuracy=0.8
    )
    
    print("\n🎉 Training pipeline completed!")
    print("📁 Check '/kaggle/working/runs/train/kaggle_optimized_model' for results.")
    print("📥 Download the best.pt model for deployment.")

if __name__ == "__main__":
    main()
